#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token输入对话框
用于手动输入网页token和sessionid
"""

import tkinter as tk
from tkinter import ttk, messagebox
import re


class TokenInputDialog:
    """Token输入对话框类"""
    
    def __init__(self, parent):
        self.parent = parent
        self.result = None  # 存储结果：{'sessionid': 'xxx', 'token': 'xxx'} 或 None
        self.dialog = None
        self.input_text = None
        self.sessionid_var = None
        self.token_var = None
        
    def show_dialog(self):
        """显示对话框并返回结果"""
        self.create_dialog()
        self.dialog.wait_window()  # 等待对话框关闭
        return self.result
    
    def create_dialog(self):
        """创建对话框界面"""
        # 创建模态对话框
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("输入网页Token")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        self.dialog.transient(self.parent)  # 设置为父窗口的子窗口
        self.dialog.grab_set()  # 设置为模态对话框
        
        # 居中显示
        self.center_dialog()
        
        # 创建主框架
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题说明
        title_label = ttk.Label(main_frame, 
                               text="请粘贴网页中的JavaScript内容，程序将自动提取sessionid和token",
                               font=("微软雅黑", 10))
        title_label.pack(pady=(0, 10))
        
        # 输入框标签
        input_label = ttk.Label(main_frame, text="网页内容输入框：", font=("微软雅黑", 9))
        input_label.pack(anchor=tk.W)
        
        # 输入框框架
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 10))
        
        # 文本输入框（带滚动条）
        self.input_text = tk.Text(input_frame, wrap=tk.WORD, font=("Consolas", 9))
        scrollbar = ttk.Scrollbar(input_frame, orient=tk.VERTICAL, command=self.input_text.yview)
        self.input_text.configure(yscrollcommand=scrollbar.set)
        
        self.input_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定输入事件
        self.input_text.bind('<KeyRelease>', self.on_text_change)
        self.input_text.bind('<Button-1>', self.on_text_change)
        self.input_text.bind('<Control-v>', self.on_paste)
        
        # 解析结果显示框架
        result_frame = ttk.LabelFrame(main_frame, text="解析结果", padding="10")
        result_frame.pack(fill=tk.X, pady=(0, 10))
        
        # SessionID显示
        sessionid_frame = ttk.Frame(result_frame)
        sessionid_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(sessionid_frame, text="SessionID:", font=("微软雅黑", 9)).pack(side=tk.LEFT)
        self.sessionid_var = tk.StringVar()
        sessionid_entry = ttk.Entry(sessionid_frame, textvariable=self.sessionid_var, 
                                   state="readonly", font=("Consolas", 9))
        sessionid_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))
        
        # Token显示
        token_frame = ttk.Frame(result_frame)
        token_frame.pack(fill=tk.X)
        
        ttk.Label(token_frame, text="Token:", font=("微软雅黑", 9)).pack(side=tk.LEFT, padx=(0, 20))
        self.token_var = tk.StringVar()
        token_entry = ttk.Entry(token_frame, textvariable=self.token_var, 
                               state="readonly", font=("Consolas", 9))
        token_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 取消按钮
        cancel_button = ttk.Button(button_frame, text="取消", command=self.on_cancel)
        cancel_button.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 确定按钮
        self.ok_button = ttk.Button(button_frame, text="确定", command=self.on_ok, state="disabled")
        self.ok_button.pack(side=tk.RIGHT)
        
        # 清空按钮
        clear_button = ttk.Button(button_frame, text="清空", command=self.on_clear)
        clear_button.pack(side=tk.LEFT)
        
        # 设置焦点到输入框
        self.input_text.focus_set()
    
    def center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        
        # 获取对话框尺寸
        dialog_width = self.dialog.winfo_reqwidth()
        dialog_height = self.dialog.winfo_reqheight()
        
        # 获取父窗口位置和尺寸
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # 计算居中位置
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"+{x}+{y}")
    
    def on_text_change(self, event=None):
        """文本内容变化时的处理"""
        self.parse_content()
    
    def on_paste(self, event=None):
        """粘贴事件处理"""
        # 延迟执行解析，确保粘贴内容已经插入
        self.dialog.after(100, self.parse_content)
    
    def parse_content(self):
        """解析输入内容，提取sessionid和token"""
        content = self.input_text.get("1.0", tk.END).strip()
        
        if not content:
            self.sessionid_var.set("")
            self.token_var.set("")
            self.ok_button.config(state="disabled")
            return
        
        # 使用正则表达式提取sessionid和token
        sessionid_match = re.search(r'"sessionid"\s*:\s*"([^"]+)"', content)
        token_match = re.search(r'"token"\s*:\s*"([^"]+)"', content)
        
        sessionid = sessionid_match.group(1) if sessionid_match else ""
        token = token_match.group(1) if token_match else ""
        
        # 更新显示
        self.sessionid_var.set(sessionid)
        self.token_var.set(token)
        
        # 检查是否都解析成功
        if sessionid and token:
            self.ok_button.config(state="normal")
        else:
            self.ok_button.config(state="disabled")
            
            # 如果有内容但解析失败，显示提示
            if content and (not sessionid or not token):
                missing = []
                if not sessionid:
                    missing.append("sessionid")
                if not token:
                    missing.append("token")
                
                # 这里不立即显示错误，等用户点击确定时再提示
    
    def on_clear(self):
        """清空输入框"""
        self.input_text.delete("1.0", tk.END)
        self.sessionid_var.set("")
        self.token_var.set("")
        self.ok_button.config(state="disabled")
        self.input_text.focus_set()
    
    def on_ok(self):
        """确定按钮点击处理"""
        sessionid = self.sessionid_var.get().strip()
        token = self.token_var.get().strip()
        
        if not sessionid or not token:
            # 显示错误提示
            missing = []
            if not sessionid:
                missing.append("sessionid")
            if not token:
                missing.append("token")
            
            messagebox.showerror("解析失败", 
                               f"无法从输入内容中解析出有效的 {' 和 '.join(missing)}！\n\n"
                               f"请确保输入内容包含类似以下格式的数据：\n"
                               f'"sessionid":"xxxxxxxxx"\n'
                               f'"token":"xxxxxxxxx"')
            
            # 清空输入框，等待重新输入
            self.on_clear()
            return
        
        # 保存结果并关闭对话框
        self.result = {
            'sessionid': sessionid,
            'token': token
        }
        self.dialog.destroy()
    
    def on_cancel(self):
        """取消按钮点击处理"""
        self.result = None
        self.dialog.destroy()


# 测试代码
if __name__ == "__main__":
    def test_dialog():
        root = tk.Tk()
        root.title("测试主窗口")
        root.geometry("400x300")
        
        def show_token_dialog():
            dialog = TokenInputDialog(root)
            result = dialog.show_dialog()
            if result:
                print(f"获取到token: {result}")
            else:
                print("用户取消了操作")
        
        btn = ttk.Button(root, text="打开Token对话框", command=show_token_dialog)
        btn.pack(pady=50)
        
        root.mainloop()
    
    test_dialog()
