#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenWrt Passwall 客户端
处理与OpenWrt系统的通信
"""

import requests
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from config import Config
from html_parser import PasswallHtmlParser
from logger import request_logger

class OpenWrtClient:
    def __init__(self, host, username, password):
        # 确保host包含协议前缀
        if not host.startswith(('http://', 'https://')):
            host = f"http://{host}"
        self.host = host.rstrip('/')
        self.username = username
        self.password = password
        self.session = requests.Session()

        # 优化Session配置
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry

        # 禁用重试策略 - 避免重复创建配置
        retry_strategy = Retry(
            total=0,                    # 禁用重试：设置为0
            backoff_factor=0,          # 禁用退避
            status_forcelist=[],       # 清空重试状态码列表
        )

        # 配置HTTP适配器
        adapter = HTTPAdapter(
            pool_connections=10,
            pool_maxsize=20,
            max_retries=retry_strategy
        )

        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
            'Connection': 'keep-alive'
        })
        self.token = None
        self.session_id = None
        self.parser = PasswallHtmlParser()

        # 性能优化：4个独立的调试文件保存选项
        self.save_create_files = False  # 配置创建请求包和响应包
        self.save_delete_files = False  # 配置删除请求包
        self.save_token_files = False   # token页面响应
        self.save_batch_files = False   # 批量配置提交请求包和响应包
        
    def login(self):
        """登录OpenWrt后台"""
        try:
            request_logger.log_message("开始登录OpenWrt后台", "INFO")

            # 构建登录URL
            login_url = f"{self.host}/cgi-bin/luci/"

            # 准备登录数据
            login_data = {
                'luci_username': self.username,
                'luci_password': self.password
            }

            # 记录请求信息
            request_logger.log_request("POST", login_url,
                                     headers=dict(self.session.headers),
                                     data=login_data)

            # 发送登录请求
            response = self.session.post(login_url, data=login_data, allow_redirects=False, timeout=30)

            # 记录响应信息
            request_logger.log_response(response, "登录请求")

            # 检查是否登录成功（通过Set-Cookie头判断）
            if response.status_code == 302 and 'sysauth_http' in response.headers.get('Set-Cookie', ''):
                # 提取session ID
                cookie_header = response.headers.get('Set-Cookie', '')
                self.session_id = self.parser.extract_session_id(cookie_header)

                if self.session_id:
                    request_logger.log_message(f"✅ 登录成功！Session ID: {self.session_id}", "SUCCESS")
                    print(f"✅ 登录成功！获取到sysauth_http: {self.session_id}")
                    return True
                else:
                    request_logger.log_message("❌ 登录失败：无法提取session ID", "ERROR")
                    return False
            else:
                request_logger.log_message(f"❌ 登录失败 - 状态码: {response.status_code}, Cookie: {response.headers.get('Set-Cookie', 'None')}", "ERROR")
                return False

        except Exception as e:
            request_logger.log_error(e, "登录过程中发生异常")
            return False
            
    def get_nodes(self):
        """获取节点列表"""
        try:
            # 优先访问节点列表页面
            import time
            timestamp = int(time.time() * 1000)  # 毫秒时间戳
            node_list_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2/node_list"

            # 记录请求
            request_logger.log_request("GET", node_list_url, headers=dict(self.session.headers))

            # 添加时间戳参数防止缓存
            response = self.session.get(node_list_url, params={'_': timestamp})

            # 记录响应
            request_logger.log_response(response)

            if response.status_code == 200:
                # 使用新的节点列表页面提取方法
                nodes = self.parser.extract_nodes(response.text, is_node_list_page=True)

                if nodes:
                    request_logger.log_message(f"✅ 从节点列表页面成功获取到 {len(nodes)} 个节点", "SUCCESS")
                    return nodes
                else:
                    request_logger.log_message("⚠️ 节点列表页面未找到节点，尝试备用方法", "WARNING")
            else:
                request_logger.log_message(f"⚠️ 访问节点列表页面失败，状态码: {response.status_code}，尝试备用方法", "WARNING")

            # 备用方法：访问主Passwall2页面
            passwall_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2"

            # 记录请求
            request_logger.log_request("GET", passwall_url, headers=dict(self.session.headers))

            response = self.session.get(passwall_url)

            # 记录响应
            request_logger.log_response(response)

            if response.status_code != 200:
                request_logger.log_message(f"❌ 访问Passwall2页面失败，状态码: {response.status_code}", "ERROR")
                return []

            # 提取token
            token = self.parser.extract_token(response.text)
            if token:
                self.token = token
                request_logger.log_message(f"🔑 提取到的token: {token}", "INFO")
            else:
                request_logger.log_message("❌ 未能提取到token", "ERROR")

            # 提取已存在的socks配置
            existing_socks = self.parser.extract_existing_socks_configs(response.text)
            if existing_socks:
                request_logger.log_message(f"📋 已存在的socks配置: {existing_socks}", "INFO")
            else:
                request_logger.log_message("📋 未找到已存在的socks配置", "INFO")

            # 解析节点信息（使用备用方法）
            nodes = self.parser.extract_nodes(response.text, is_node_list_page=False)

            # 如果没有找到节点，返回一些测试节点用于演示
            if not nodes:
                request_logger.log_message("⚠️ 未找到节点，使用测试节点", "WARNING")
                test_nodes = [
                    {'id': 'NHbySajH', 'name': '香港节点1', 'region': '香港', 'type': 'Xray', 'address': '***********', 'port': '443'},
                    {'id': 'test_us_1', 'name': '美国节点1', 'region': '美国', 'type': 'Xray', 'address': '***********', 'port': '443'},
                    {'id': 'test_jp_1', 'name': '日本节点1', 'region': '日本', 'type': 'Xray', 'address': '***********', 'port': '443'},
                ]
                return test_nodes

            request_logger.log_message(f"✅ 成功获取到 {len(nodes)} 个节点", "SUCCESS")
            return nodes

        except Exception as e:
            error_msg = f"获取节点列表失败: {e}"
            print(error_msg)
            request_logger.log_error(e, "获取节点列表过程中发生异常")
            return []

    def get_token_and_socks_config(self):
        """获取token和已存在的socks配置"""
        try:
            # 访问Passwall2设置页面
            import time
            timestamp = int(time.time() * 1000)  # 毫秒时间戳
            passwall_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2/settings"

            # 记录请求
            request_logger.log_request("GET", passwall_url, headers=dict(self.session.headers))

            # 添加时间戳参数防止缓存
            response = self.session.get(passwall_url, params={'_': timestamp})

            # 记录响应
            request_logger.log_response(response)

            # 保存获取token页面的完整响应到本地文件（可选）
            if self.save_token_files:
                self._save_token_page_response(response)

            if response.status_code != 200:
                request_logger.log_message(f"❌ 访问Passwall2主页面失败，状态码: {response.status_code}", "ERROR")
                return None, []

            # 提取token
            token = self.parser.extract_token(response.text)
            if token:
                self.token = token
                request_logger.log_message(f"🔑 成功提取token: {token}", "SUCCESS")
                print(f"🔑 获取到token: {token}")
            else:
                request_logger.log_message("❌ 未能提取到token", "ERROR")
                print("❌ 未能提取到token")

            # 提取已存在的socks配置
            existing_socks = self.parser.extract_existing_socks_configs(response.text)
            if existing_socks:
                request_logger.log_message(f"📋 已存在的socks配置: {existing_socks}", "SUCCESS")
                print(f"📋 找到 {len(existing_socks)} 个已存在的socks配置:")
                for i, config_id in enumerate(existing_socks, 1):
                    print(f"  {i}. {config_id}")
            else:
                request_logger.log_message("📋 未找到已存在的socks配置", "INFO")
                print("📋 未找到已存在的socks配置")

            return token, existing_socks

        except Exception as e:
            error_msg = f"获取token和socks配置失败: {e}"
            print(error_msg)
            request_logger.log_error(e, "获取token和socks配置过程中发生异常")
            return None, []

    def get_current_socks_configs(self):
        """获取当前存在的所有Socks配置ID（用于实时计数）"""
        try:
            # 访问Passwall2设置页面
            import time
            timestamp = int(time.time() * 1000)  # 毫秒时间戳
            passwall_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2/settings"

            # 添加时间戳参数防止缓存
            response = self.session.get(passwall_url, params={'_': timestamp})

            if response.status_code != 200:
                print(f"❌ 访问Passwall2主页面失败，状态码: {response.status_code}")
                return []

            # 使用相同的解析器提取已存在的socks配置
            existing_socks = self.parser.extract_existing_socks_configs(response.text)
            return existing_socks

        except Exception as e:
            print(f"获取当前配置失败: {e}")
            return []

    def assign_ports_to_nodes(self, nodes):
        """智能管理节点信息：与现有nodes_data.json匹配，保持name和socks_port固定映射"""
        from config import REGION_PORT_MAP
        import json
        import os

        print("🔄 开始智能节点信息管理...")

        # 加载现有的节点数据
        existing_nodes = self.load_existing_nodes()

        # 创建现有节点的name到节点数据的映射
        existing_nodes_map = {node['name']: node for node in existing_nodes}

        # 统计信息
        updated_count = 0
        new_count = 0
        disabled_count = 0

        # 处理新获取的节点
        processed_nodes = []

        print(f"📊 现有节点数据: {len(existing_nodes)} 个")
        print(f"📊 新获取节点: {len(nodes)} 个")

        # 第一步：处理新获取的节点
        for node in nodes:
            node_name = node['name']

            if node_name in existing_nodes_map:
                # 节点已存在，只更新可变字段
                existing_node = existing_nodes_map[node_name].copy()

                # 只更新id、address、port字段
                existing_node['id'] = node['id']
                existing_node['address'] = node['address']
                existing_node['port'] = node['port']
                existing_node['is_use'] = True  # 标记为启用

                # 保持name、region、socks_port不变
                processed_nodes.append(existing_node)
                updated_count += 1

                print(f"🔄 更新节点: {node_name} -> 端口 {existing_node['socks_port']} (保持不变)")

            else:
                # 新增节点，需要分配新的socks_port
                new_node = node.copy()
                new_socks_port = self.assign_new_socks_port(node['region'], existing_nodes + processed_nodes)
                new_node['socks_port'] = new_socks_port
                new_node['is_use'] = True

                processed_nodes.append(new_node)
                new_count += 1

                print(f"🆕 新增节点: {node_name} -> 端口 {new_socks_port}")

        # 第二步：检查废弃的节点
        new_node_names = {node['name'] for node in nodes}

        for existing_node in existing_nodes:
            if existing_node['name'] not in new_node_names:
                # 节点已废弃，标记为不可用
                disabled_node = existing_node.copy()
                disabled_node['is_use'] = False
                processed_nodes.append(disabled_node)
                disabled_count += 1

                print(f"⚠️ 废弃节点: {existing_node['name']} -> 端口 {existing_node['socks_port']} (已停用)")

        # 按socks_port排序，保持顺序一致
        processed_nodes.sort(key=lambda x: x['socks_port'])

        # 保存更新后的节点数据
        self.save_nodes_data(processed_nodes)

        # 输出统计信息
        print(f"\n📋 节点信息管理完成:")
        print(f"  🔄 更新节点: {updated_count} 个")
        print(f"  🆕 新增节点: {new_count} 个")
        print(f"  ⚠️ 废弃节点: {disabled_count} 个")
        print(f"  📊 总计节点: {len(processed_nodes)} 个")

        if disabled_count > 0:
            disabled_names = [node['name'] for node in processed_nodes if not node.get('is_use', True)]
            print(f"  🚨 已停用的节点: {', '.join(disabled_names)}")

        # 只返回启用的节点用于后续处理
        active_nodes = [node for node in processed_nodes if node.get('is_use', True)]
        print(f"  ✅ 可用节点: {len(active_nodes)} 个")

        return active_nodes

    def load_existing_nodes(self):
        """加载现有的节点数据"""
        import json
        import os

        if not os.path.exists('nodes_data.json'):
            print("📄 nodes_data.json 不存在，将创建新文件")
            return []

        try:
            with open('nodes_data.json', 'r', encoding='utf-8') as f:
                existing_nodes = json.load(f)

            # 为旧数据添加is_use字段（向后兼容）
            for node in existing_nodes:
                if 'is_use' not in node:
                    node['is_use'] = True

            print(f"📄 成功加载现有节点数据: {len(existing_nodes)} 个")
            return existing_nodes

        except Exception as e:
            print(f"❌ 加载现有节点数据失败: {e}")
            return []

    def assign_new_socks_port(self, region, all_nodes):
        """为新增节点分配socks_port（确保端口 < 70000）"""
        from config import Config

        # 获取地区基础端口和端口范围限制
        base_port = Config.REGION_PORTS.get(region, 50000)  # 未知地区使用50000
        port_limits = Config.PORT_LIMITS.get(region, Config.PORT_LIMITS.get("其他"))

        print(f"🔍 为地区 '{region}' 分配端口，基础端口: {base_port}, 范围: {port_limits['min']}-{port_limits['max']}")

        # 获取该地区已使用的端口号（只考虑在合理范围内的端口）
        region_ports = []
        for node in all_nodes:
            if node.get('region') == region and 'socks_port' in node:
                port = node['socks_port']
                # 只统计在合理范围内的端口
                if port_limits['min'] <= port <= port_limits['max']:
                    region_ports.append(port)

        # 找到该地区的下一个可用端口
        if not region_ports:
            # 该地区第一个节点，使用范围最小值
            new_port = port_limits['min']
        else:
            # 找到最大端口号，然后+1
            max_port = max(region_ports)
            new_port = max_port + 1

            # 检查是否超出地区范围
            if new_port > port_limits['max']:
                print(f"⚠️ 地区 '{region}' 端口已满，尝试从范围内寻找空隙...")
                # 尝试在范围内找到空隙
                new_port = self.find_available_port_in_range(port_limits['min'], port_limits['max'], region_ports)

                if new_port is None:
                    print(f"❌ 地区 '{region}' 端口范围已满，分配到其他地区...")
                    # 分配到"其他"地区
                    return self.assign_new_socks_port("其他", all_nodes)

        # 最终检查：确保端口不超过全局上限
        if new_port >= Config.MAX_SOCKS_PORT:
            print(f"❌ 端口 {new_port} 超过全局上限 {Config.MAX_SOCKS_PORT}，分配失败")
            raise ValueError(f"无法分配端口：已达到全局上限 {Config.MAX_SOCKS_PORT}")

        print(f"✅ 为地区 '{region}' 分配端口: {new_port}")
        return new_port

    def find_available_port_in_range(self, min_port, max_port, used_ports):
        """在指定范围内寻找可用端口"""
        used_set = set(used_ports)
        for port in range(min_port, max_port + 1):
            if port not in used_set:
                return port
        return None

    def save_nodes_data(self, nodes):
        """保存节点数据到JSON文件"""
        import json

        try:
            with open('nodes_data.json', 'w', encoding='utf-8') as f:
                json.dump(nodes, f, ensure_ascii=False, indent=2)
            print(f"💾 已更新nodes_data.json，共 {len(nodes)} 个节点")
            request_logger.log_message(f"💾 已更新nodes_data.json，共 {len(nodes)} 个节点", "INFO")
        except Exception as e:
            print(f"❌ 保存节点数据失败: {e}")

    def _save_token_page_response(self, response):
        """保存获取token页面的响应到本地文件"""
        try:
            import datetime

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"token页面响应_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("获取Token页面响应信息\n")
                f.write("=" * 50 + "\n\n")

                f.write(f"时间: {datetime.datetime.now().isoformat()}\n")
                f.write(f"请求URL: {response.url}\n")
                f.write(f"状态码: {response.status_code}\n")
                f.write(f"状态: {response.reason}\n\n")

                f.write("响应头:\n")
                f.write("-" * 30 + "\n")
                for key, value in response.headers.items():
                    f.write(f"{key}: {value}\n")
                f.write("\n")

                f.write("响应内容 (HTML):\n")
                f.write("-" * 30 + "\n")
                f.write(response.text)
                f.write("\n")

            print(f"💾 Token页面响应已保存到: {filename}")
            request_logger.log_message(f"💾 Token页面响应已保存到: {filename}", "INFO")

        except Exception as e:
            print(f"❌ 保存Token页面响应失败: {e}")

    def _extract_region_from_name(self, name):
        """从节点名称中提取地区信息"""
        region_keywords = {
            '香港': ['香港', 'HK', 'Hong Kong', 'hongkong'],
            '美国': ['美国', 'US', 'USA', 'United States', 'america'],
            '日本': ['日本', 'JP', 'Japan', 'tokyo'],
            '新加坡': ['新加坡', 'SG', 'Singapore'],
            '韩国': ['韩国', 'KR', 'Korea', 'seoul'],
            '台湾': ['台湾', 'TW', 'Taiwan'],
            '英国': ['英国', 'UK', 'Britain', 'london'],
            '德国': ['德国', 'DE', 'Germany', 'berlin'],
            '法国': ['法国', 'FR', 'France', 'paris'],
            '加拿大': ['加拿大', 'CA', 'Canada'],
            '澳大利亚': ['澳大利亚', 'AU', 'Australia'],
            '荷兰': ['荷兰', 'NL', 'Netherlands'],
            '瑞士': ['瑞士', 'CH', 'Switzerland'],
            '俄罗斯': ['俄罗斯', 'RU', 'Russia'],
            '印度': ['印度', 'IN', 'India'],
            '泰国': ['泰国', 'TH', 'Thailand'],
            '马来西亚': ['马来西亚', 'MY', 'Malaysia'],
            '菲律宾': ['菲律宾', 'PH', 'Philippines'],
            '越南': ['越南', 'VN', 'Vietnam'],
            '印尼': ['印尼', 'ID', 'Indonesia'],
        }

        name_upper = name.upper()
        for region, keywords in region_keywords.items():
            for keyword in keywords:
                if keyword.upper() in name_upper:
                    return region

        return '其他'
            
    def get_existing_socks(self):
        """获取现有的Socks配置"""
        try:
            # 访问Passwall2页面
            passwall_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2"
            response = self.session.get(passwall_url)
            
            if response.status_code != 200:
                return []
                
            # 解析HTML获取现有的Socks配置
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找删除按钮或配置项
            # 根据您提供的删除包信息，查找类似 cbi.rts.passwall2.xxx 的项
            existing_configs = []
            
            # 查找所有包含删除操作的表单元素
            delete_inputs = soup.find_all('input', {'name': re.compile(r'cbi\.rts\.passwall2\.')})
            for input_elem in delete_inputs:
                config_id = input_elem.get('name').replace('cbi.rts.passwall2.', '')
                existing_configs.append(config_id)
                
            return existing_configs
            
        except Exception as e:
            print(f"获取现有Socks配置失败: {e}")
            return []
            
    def delete_existing_socks(self, existing_configs):
        """删除现有的Socks配置"""
        try:
            if not existing_configs:
                return True

            # 构建删除请求
            delete_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2/settings"

            # 准备删除数据 - 使用multipart/form-data格式
            delete_files = {
                'token': (None, self.token),
                'cbi.submit': (None, '1'),
                'cbi.apply': (None, '1')
            }

            # 添加每个配置的删除标记
            for config_id in existing_configs:
                delete_files[f'cbi.rts.passwall2.{config_id}'] = (None, '删除')

            # 保存删除请求数据包到本地文件（可选）
            if self.save_delete_files:
                self._save_delete_request_packet(delete_url, delete_files, existing_configs)

            # 准备删除请求的专用请求头
            # 从session中获取cookie
            cookie_header = ""
            for cookie in self.session.cookies:
                if cookie.name == 'sysauth_http':
                    cookie_header = f"sysauth_http={cookie.value}"
                    break

            # 解析URL获取host
            from urllib.parse import urlparse
            parsed_url = urlparse(delete_url)

            delete_headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Encoding': 'gzip, deflate',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'max-age=0',
                'Origin': self.host,
                'Referer': delete_url,
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            # 使用MultipartEncoder构建请求体，与创建配置时保持一致
            import requests_toolbelt
            from requests_toolbelt.multipart.encoder import MultipartEncoder

            multipart_data = MultipartEncoder(
                fields=delete_files,
                boundary='----WebKitFormBoundaryACE5azrrYYWFcSp0'
            )

            # 更新Content-Type头
            delete_headers['Content-Type'] = multipart_data.content_type

            # 记录请求
            request_logger.log_request("POST", delete_url, headers=delete_headers, data="multipart_data")

            # 发送删除请求 - 使用data参数发送multipart/form-data，与创建配置保持一致
            response = self.session.post(delete_url, data=multipart_data, headers=delete_headers)

            # 记录响应
            request_logger.log_response(response)

            # 保存删除响应数据包到本地文件（可选）
            if self.save_delete_files:
                self._save_delete_response_packet(response)

            success = response.status_code == 200 or response.status_code == 302
            if success:
                request_logger.log_message("✅ 删除socks配置请求成功", "SUCCESS")
            else:
                request_logger.log_message(f"❌ 删除socks配置失败，状态码: {response.status_code}", "ERROR")

            return success

        except Exception as e:
            print(f"删除现有配置失败: {e}")
            request_logger.log_error(e, "删除socks配置过程中发生异常")
            return False

    def batch_submit_all_configs(self, config_ids, nodes, ports, enable_retry=True):
        """批量提交所有配置参数

        Args:
            config_ids: 配置ID列表
            nodes: 节点列表
            ports: 端口列表
            enable_retry: 是否启用二次登录重新提交，默认True
        """
        try:
            # 添加详细的调试信息
            print(f"🔍 批量提交参数检查:")
            print(f"  - 配置ID数量: {len(config_ids) if config_ids else 0}")
            print(f"  - 节点数量: {len(nodes) if nodes else 0}")
            print(f"  - 端口数量: {len(ports) if ports else 0}")

            if config_ids:
                print(f"  - 配置ID列表: {config_ids}")
            if nodes:
                print(f"  - 节点列表: {[node.get('name', 'Unknown') for node in nodes[:3]]}{'...' if len(nodes) > 3 else ''}")
            if ports:
                print(f"  - 端口列表: {ports}")

            if not config_ids or len(config_ids) != len(nodes) or len(config_ids) != len(ports):
                print("❌ 配置ID、节点、端口数量不匹配")
                print(f"   期望: 配置ID={len(config_ids) if config_ids else 0}, 节点={len(nodes) if nodes else 0}, 端口={len(ports) if ports else 0}")
                return False

            print(f"🔄 开始批量提交 {len(config_ids)} 个配置...")

            # 记录当前认证信息（用于对比）
            old_token = self.token
            old_session_id = self.session_id
            old_cookies = {}
            for cookie in self.session.cookies:
                if cookie.name == 'sysauth_http':
                    old_cookies['sysauth_http'] = cookie.value
                    break

            print(f"🔍 当前认证状态:")
            print(f"  Token: {old_token}")
            print(f"  SessionID: {old_session_id}")
            print(f"  Cookie: {old_cookies}")

            # 第一次尝试：使用当前认证状态直接批量提交
            print(f"🚀 第一次尝试：使用当前认证状态批量提交...")
            first_attempt_success = self._attempt_batch_submit(config_ids, nodes, ports)

            # 屏蔽第一次成功的判断，强制进行二次登录
            if first_attempt_success:
                print("🎉 第一次批量提交HTTP状态正常，但强制进行二次登录重新提交")
            else:
                print("⚠️ 第一次批量提交失败")

            # 强制进行二次登录，忽略用户设置
            print("🔄 强制执行退出-重新登录流程...")

            # 退出登录，清理会话状态
            print(f"🔄 执行退出-重新登录流程...")
            logout_success = self.logout()

            if not logout_success:
                print("⚠️ 退出登录失败，但继续尝试重新登录...")
                print("💡 可能session已过期，直接重新登录即可")

            # 重新登录，获取全新会话
            print(f"🔄 重新登录...")
            if not self.login():
                print("❌ 重新登录失败，批量提交终止")
                return False

            # 显示认证信息变化
            new_cookies = {}
            for cookie in self.session.cookies:
                if cookie.name == 'sysauth_http':
                    new_cookies['sysauth_http'] = cookie.value
                    break

            print(f"🔍 重新登录后认证状态:")
            print(f"  Token: {self.token}")
            print(f"  SessionID: {self.session_id}")
            print(f"  Cookie: {new_cookies}")

            print(f"🔄 认证信息对比:")
            print(f"更新token: {old_token} -> {self.token}")
            print(f"更新sessionid: {old_session_id} -> {self.session_id}")
            print(f"更新cookie: {old_cookies.get('sysauth_http', 'None')} -> {new_cookies.get('sysauth_http', 'None')}")

            # 第二次尝试：使用全新认证状态批量提交
            print(f"🚀 第二次尝试：使用全新认证状态批量提交...")
            second_attempt_success = self._attempt_batch_submit(config_ids, nodes, ports)

            if second_attempt_success:
                print("🎉 第二次批量提交成功！")
                return True
            else:
                print("❌ 第二次批量提交也失败了")
                return False

        except Exception as e:
            print(f"❌ 批量提交过程中发生异常: {e}")
            request_logger.log_error(e, "批量提交过程中发生异常")
            return False

    def _attempt_batch_submit(self, config_ids, nodes, ports):
        """尝试批量提交配置参数"""
        try:
            # 获取主配置ID
            main_config_id = self.get_main_config_id()
            if not main_config_id:
                print("❌ 无法获取主配置ID")
                return False

            # 构建批量提交URL
            submit_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2/settings"

            # 构建批量提交数据
            batch_data = {
                'token': (None, self.token),
                'cbi.submit': (None, '1')
            }

            # 添加主配置字段（根据成功包的格式）
            print(f"📋 添加主配置字段: {main_config_id}")

            # 主配置的enabled字段
            batch_data[f'cbi.cbe.passwall2.{main_config_id}.enabled'] = (None, '1')

            # 主配置的基本字段
            batch_data[f'cbid.passwall2.{main_config_id}.node'] = (None, 'nil')
            batch_data[f'cbi.cbe.passwall2.{main_config_id}.localhost_proxy'] = (None, '1')
            batch_data[f'cbid.passwall2.{main_config_id}.localhost_proxy'] = (None, '1')
            batch_data[f'cbi.cbe.passwall2.{main_config_id}.client_proxy'] = (None, '1')
            batch_data[f'cbid.passwall2.{main_config_id}.client_proxy'] = (None, '1')
            batch_data[f'cbid.passwall2.{main_config_id}.node_socks_port'] = (None, '1070')

            # DNS相关字段
            batch_data[f'cbid.passwall2.{main_config_id}.remote_dns_protocol'] = (None, 'tcp')
            batch_data[f'cbid.passwall2.{main_config_id}.remote_dns'] = (None, '*******')
            batch_data[f'cbid.passwall2.{main_config_id}.remote_dns_detour'] = (None, 'remote')
            batch_data[f'cbi.cbe.passwall2.{main_config_id}.remote_fakedns'] = (None, '1')
            batch_data[f'cbid.passwall2.{main_config_id}.remote_dns_query_strategy'] = (None, 'UseIPv4')

            # 其他配置字段
            batch_data[f'cbi.cbe.passwall2.{main_config_id}.write_ipset_direct'] = (None, '1')
            batch_data[f'cbid.passwall2.{main_config_id}.write_ipset_direct'] = (None, '1')
            batch_data[f'cbi.cbe.passwall2.{main_config_id}.log_node'] = (None, '1')
            batch_data[f'cbid.passwall2.{main_config_id}.log_node'] = (None, '1')
            batch_data[f'cbid.passwall2.{main_config_id}.loglevel'] = (None, 'warning')

            # 关键字段：启用Socks功能（两种格式的主开关）
            batch_data[f'cbi.cbe.passwall2.{main_config_id}.socks_enabled'] = (None, '1')
            batch_data[f'cbid.passwall2.{main_config_id}.socks_enabled'] = (None, '1')

            print(f"✅ 主配置字段已添加: {main_config_id}")

            # 为每个Socks配置添加字段
            print(f"📋 添加 {len(config_ids)} 个Socks配置字段...")
            for i, (config_id, node, port) in enumerate(zip(config_ids, nodes, ports)):
                # 两种enabled字段格式
                batch_data[f'cbi.cbe.passwall2.{config_id}.enabled'] = (None, '1')
                batch_data[f'cbid.passwall2.{config_id}.enabled'] = (None, '1')

                # 节点和端口设置
                batch_data[f'cbid.passwall2.{config_id}.node'] = (None, node['id'])
                batch_data[f'cbid.passwall2.{config_id}.port'] = (None, str(port))
                batch_data[f'cbid.passwall2.{config_id}.http_port'] = (None, '0')

                print(f"  Socks配置 {i+1}: {config_id} -> 节点 {node['name']} ({node['id']}) -> 端口 {port}")

            # 添加应用字段
            batch_data['cbi.apply'] = (None, '1')

            print(f"📊 总字段数: {len(batch_data)} (主配置: 16个 + Socks配置: {len(config_ids)*5}个 + 其他: 3个)")

            # 构建请求头，包含4个关键参数
            # 获取Cookie
            cookie_header = ""
            for cookie in self.session.cookies:
                if cookie.name == 'sysauth_http':
                    cookie_header = f"sysauth_http={cookie.value}"
                    break

            submit_headers = {
                'Cookie': cookie_header,
                'Referer': submit_url,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            # 使用MultipartEncoder构建请求体
            from requests_toolbelt.multipart.encoder import MultipartEncoder

            multipart_data = MultipartEncoder(
                fields=batch_data,
                boundary='----WebKitFormBoundaryBatchSubmit'
            )

            # 更新Content-Type头
            submit_headers['Content-Type'] = multipart_data.content_type

            print(f"🔍 提交URL: {submit_url}")
            print(f"🔍 请求头: {submit_headers}")

            # 保存批量提交请求包（可选）
            if self.save_batch_files:
                self._save_batch_submit_request_packet(submit_url, batch_data, config_ids, nodes, ports, main_config_id)

            # 发送批量提交请求
            print(f"🚀 发送批量提交请求...")
            response = self.session.post(submit_url, data=multipart_data, headers=submit_headers)

            print(f"🔍 批量提交响应状态码: {response.status_code}")

            # 保存批量提交响应包（可选）
            if self.save_batch_files:
                self._save_batch_submit_response_packet(response, config_ids)

            success = response.status_code == 200 or response.status_code == 302
            if success:
                print(f"✅ 批量提交成功！已设置 {len(config_ids)} 个配置")
                request_logger.log_message(f"✅ 批量提交成功！已设置 {len(config_ids)} 个配置", "SUCCESS")
            else:
                print(f"❌ 批量提交失败，状态码: {response.status_code}")
                print(f"🔍 响应内容前200字符: {response.text[:200]}")
                request_logger.log_message(f"❌ 批量提交失败，状态码: {response.status_code}", "ERROR")

            return success

        except Exception as e:
            print(f"❌ 批量提交失败: {e}")
            request_logger.log_error(e, "批量提交配置过程中发生异常")
            return False

    def _verify_batch_submit_result_by_get(self, expected_configs):
        """
        通过GET请求验证批量提交结果：检查配置-节点对应关系是否正确

        Args:
            expected_configs: 预期的配置列表 [(配置ID, 节点ID), ...]

        Returns:
            bool: 是否100%匹配
        """
        try:
            import re

            print(f"🔍 验证预期配置数量: {len(expected_configs)}")
            print(f"🔍 预期配置列表:")
            for i, (config_id, node_id) in enumerate(expected_configs):
                print(f"  配置 {i+1}: {config_id} → 节点 {node_id}")

            # GET请求获取页面内容
            verify_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2"

            # 构建请求头
            headers = {
                'Cookie': f'sysauth_http={self.session_id}',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            print(f"🔍 GET请求验证页面: {verify_url}")
            response = self.session.get(verify_url, headers=headers)

            if response.status_code != 200:
                print(f"❌ GET验证页面失败，状态码: {response.status_code}")
                return False

            # 使用新的正则表达式提取实际的配置-节点对应关系
            pattern = r'<div id="cbi-passwall2-(.*?)-node"[\s\S]*?<div data-ui-widget="\[&#34;Select(.*?),{'
            matches = re.findall(pattern, response.text)

            print(f"🔍 从页面中提取到 {len(matches)} 个配置-节点对应关系:")
            actual_configs = {}
            for i, (config_id, raw_node_data) in enumerate(matches):
                # 处理节点数据：去掉 "&#34;" 和 ","
                processed_node_id = raw_node_data.replace('&#34;', '').replace(',', '').strip()
                actual_configs[config_id] = processed_node_id
                print(f"  实际 {i+1}: {config_id} → 节点 {processed_node_id} (原始: {raw_node_data})")

            # 验证每个预期配置是否在实际结果中正确匹配
            all_matched = True
            for config_id, expected_node_id in expected_configs:
                actual_node_id = actual_configs.get(config_id, None)

                if actual_node_id is None:
                    print(f"❌ 配置 {config_id} 在页面中未找到")
                    all_matched = False
                elif actual_node_id != expected_node_id:
                    print(f"❌ 配置 {config_id} 节点不匹配: 预期 {expected_node_id}, 实际 {actual_node_id}")
                    all_matched = False
                else:
                    print(f"✅ 配置 {config_id} 节点匹配正确: {expected_node_id}")

            if all_matched:
                print(f"🎉 所有 {len(expected_configs)} 个配置的节点设置验证通过！")
            else:
                print(f"❌ 配置验证失败，存在不匹配的配置")

            return all_matched

        except Exception as e:
            print(f"❌ 验证批量提交结果时发生异常: {e}")
            return False

    def _save_delete_request_packet(self, url, files_data, existing_configs):
        """保存删除请求数据包到本地文件"""
        try:
            import datetime
            from urllib.parse import urlparse

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"删除请求包_{timestamp}.txt"

            # 解析URL
            parsed_url = urlparse(url)

            # 生成boundary
            boundary = "----WebKitFormBoundaryACE5azrrYYWFcSp0"

            # 构建multipart/form-data请求体
            body_parts = []
            for key, (filename_part, value) in files_data.items():
                part = f"------WebKitFormBoundaryACE5azrrYYWFcSp0\n"
                part += f'Content-Disposition: form-data; name="{key}"\n\n'
                part += f"{value}\n"
                body_parts.append(part)

            # 添加结束boundary
            body_parts.append(f"------WebKitFormBoundaryACE5azrrYYWFcSp0--\n")

            request_body = "".join(body_parts)
            content_length = len(request_body.encode('utf-8'))

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("提交包：\n")

                # 请求行
                f.write(f"POST {parsed_url.path} HTTP/1.1\n")

                # 请求头 - 只保留指定的三个字段
                f.write(f"Content-Type: multipart/form-data; boundary={boundary}\n")

                # 从session中获取cookie
                cookie_header = ""
                for cookie in self.session.cookies:
                    if cookie.name == 'sysauth_http':
                        cookie_header = f"Cookie: sysauth_http={cookie.value}\n"
                        break
                if cookie_header:
                    f.write(cookie_header)

                f.write("User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\n")
                f.write("\n")

                # 请求体
                f.write(request_body)

            print(f"💾 删除请求数据包已保存到: {filename}")
            request_logger.log_message(f"💾 删除请求数据包已保存到: {filename}", "INFO")

        except Exception as e:
            print(f"❌ 保存删除请求数据包失败: {e}")

    def _save_delete_response_packet(self, response):
        """保存删除响应数据包到本地文件"""
        try:
            import datetime

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"删除响应包_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("删除响应结果:\n")
                f.write("=" * 30 + "\n")
                f.write(f"状态码: {response.status_code}\n")
                f.write(f"状态: {response.reason}\n")
                f.write("=" * 30 + "\n")
                f.write("响应内容:\n")
                f.write(response.text)

            print(f"💾 删除响应数据包已保存到: {filename}")
            request_logger.log_message(f"💾 删除响应数据包已保存到: {filename}", "INFO")

        except Exception as e:
            print(f"❌ 保存删除响应数据包失败: {e}")

    def _save_batch_submit_request_packet(self, url, batch_data, config_ids, nodes, ports, main_config_id):
        """保存批量提交请求数据包到本地文件"""
        try:
            import datetime
            from urllib.parse import urlparse

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"批量提交请求包_{timestamp}.txt"

            # 解析URL
            parsed_url = urlparse(url)

            # 生成boundary
            boundary = "----WebKitFormBoundaryBatchSubmit"

            # 构建multipart/form-data请求体
            body_parts = []
            for key, (filename_part, value) in batch_data.items():
                part = f"------WebKitFormBoundaryBatchSubmit\n"
                part += f'Content-Disposition: form-data; name="{key}"\n\n'
                part += f"{value}\n"
                body_parts.append(part)

            body = "".join(body_parts) + "------WebKitFormBoundaryBatchSubmit--\n"

            # 获取Cookie
            cookie_header = ""
            for cookie in self.session.cookies:
                if cookie.name == 'sysauth_http':
                    cookie_header = f"sysauth_http={cookie.value}"
                    break

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("批量提交请求包：\n")
                f.write("=" * 50 + "\n")
                f.write(f"POST {parsed_url.path} HTTP/1.1\n")
                f.write(f"Content-Type: multipart/form-data; boundary={boundary}\n")
                f.write(f"Cookie: {cookie_header}\n")
                f.write(f"Referer: {url}\n")
                f.write("User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\n")
                f.write("\n")
                f.write(body)

                # 添加配置信息摘要
                f.write("\n" + "=" * 50 + "\n")
                f.write("配置信息摘要：\n")
                f.write(f"主配置ID: {main_config_id}\n")
                f.write(f"Socks配置数量: {len(config_ids)}\n")
                f.write("\nSocks配置详情：\n")
                for i, (config_id, node, port) in enumerate(zip(config_ids, nodes, ports)):
                    f.write(f"  配置 {i+1}: {config_id} -> 节点 {node['name']} ({node['id']}) -> 端口 {port}\n")
                f.write(f"\n总字段数: {len(batch_data)}\n")
                f.write("字段构成: 主配置(16个) + Socks配置({0}*5={1}个) + 其他(3个) = {2}个\n".format(
                    len(config_ids), len(config_ids)*5, len(batch_data)))

            print(f"💾 批量提交请求包已保存到: {filename}")
            request_logger.log_message(f"💾 批量提交请求包已保存到: {filename}", "INFO")

        except Exception as e:
            print(f"❌ 保存批量提交请求包失败: {e}")
            request_logger.log_error(e, "保存批量提交请求包过程中发生异常")

    def _save_batch_submit_response_packet(self, response, config_ids):
        """保存批量提交响应数据包到本地文件"""
        try:
            import datetime

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"批量提交响应包_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("批量提交响应结果:\n")
                f.write("=" * 50 + "\n")
                f.write(f"状态码: {response.status_code}\n")
                f.write(f"状态: {response.reason}\n")
                f.write(f"配置数量: {len(config_ids)}\n")
                f.write(f"配置ID列表: {', '.join(config_ids)}\n")
                f.write("=" * 50 + "\n")
                f.write("响应头:\n")
                for header, value in response.headers.items():
                    f.write(f"{header}: {value}\n")
                f.write("\n")
                f.write("响应内容:\n")
                f.write(response.text)

            print(f"💾 批量提交响应包已保存到: {filename}")
            request_logger.log_message(f"💾 批量提交响应包已保存到: {filename}", "INFO")

        except Exception as e:
            print(f"❌ 保存批量提交响应包失败: {e}")
            request_logger.log_error(e, "保存批量提交响应包过程中发生异常")

    def get_main_config_id(self):
        """获取主配置ID（cfg开头的配置）并更新最新的token和sessionid"""
        try:
            settings_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2/settings"
            print(f"🔄 GET访问设置页面获取最新token和sessionid...")
            response = self.session.get(settings_url)

            if response.status_code == 200:
                import re

                # 1. 查找主配置ID，通常是cfg开头的
                # 查找类似 cbid.passwall2.cfg013fd6.enabled 的模式
                config_pattern = r'cbid\.passwall2\.(cfg[a-zA-Z0-9]+)\.enabled'
                config_matches = re.findall(config_pattern, response.text)

                main_config_id = None
                if config_matches:
                    main_config_id = config_matches[0]  # 取第一个匹配的主配置ID
                    print(f"🔍 找到主配置ID: {main_config_id}")
                    request_logger.log_message(f"🔍 找到主配置ID: {main_config_id}", "INFO")
                else:
                    print("❌ 未找到主配置ID")

                # 2. 解析并更新最新的token和sessionid
                # 查找 sessionid":"xxxxxx" 格式
                sessionid_pattern = r'"sessionid":"([a-f0-9]+)"'
                sessionid_match = re.search(sessionid_pattern, response.text)

                # 查找 token":"xxxxxx" 格式
                token_pattern = r'"token":"([a-f0-9]+)"'
                token_match = re.search(token_pattern, response.text)

                # 更新token和sessionid
                if token_match:
                    old_token = self.token
                    self.token = token_match.group(1)
                    print(f"🔑 更新token: {old_token} -> {self.token}")
                    request_logger.log_message(f"🔑 更新token: {old_token} -> {self.token}", "INFO")
                else:
                    print("⚠️ 未找到新的token，继续使用现有token")

                if sessionid_match:
                    old_sessionid = self.session_id
                    self.session_id = sessionid_match.group(1)
                    print(f"🔑 更新sessionid: {old_sessionid} -> {self.session_id}")
                    request_logger.log_message(f"🔑 更新sessionid: {old_sessionid} -> {self.session_id}", "INFO")
                else:
                    print("⚠️ 未找到新的sessionid，继续使用现有sessionid")

                return main_config_id

            else:
                print(f"❌ 获取设置页面失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ 获取主配置ID和更新token失败: {e}")
            request_logger.log_error(e, "获取主配置ID和更新token过程中发生异常")
            return None

    def logout(self):
        """退出登录"""
        try:
            logout_url = f"{self.host}/cgi-bin/luci/admin/logout"
            print(f"🔄 退出登录...")

            # 记录退出前的认证信息
            old_cookies = {}
            for cookie in self.session.cookies:
                if cookie.name == 'sysauth_http':
                    old_cookies['sysauth_http'] = cookie.value
                    break

            # 构建退出登录请求头（参考退出登录.txt）
            logout_headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Encoding': 'gzip, deflate',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Referer': f"{self.host}/cgi-bin/luci/admin/services/passwall2",
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            print(f"🔍 退出登录URL: {logout_url}")
            print(f"🔍 当前Cookie: {old_cookies}")

            # 发送退出登录请求
            response = self.session.get(logout_url, headers=logout_headers)

            print(f"🔍 退出登录响应状态码: {response.status_code}")

            if response.status_code == 200:
                print(f"✅ 退出登录成功")
                request_logger.log_message("✅ 退出登录成功", "INFO")

                # 记录退出后的cookie状态
                new_cookies = {}
                for cookie in self.session.cookies:
                    if cookie.name == 'sysauth_http':
                        new_cookies['sysauth_http'] = cookie.value
                        break

                print(f"🔍 退出前cookie: {old_cookies}")
                print(f"🔍 退出后cookie: {new_cookies}")
                return True
            elif response.status_code == 302:
                # 302重定向也可能是成功的退出
                print(f"✅ 退出登录成功（重定向）")
                print(f"🔍 重定向到: {response.headers.get('Location', 'Unknown')}")
                request_logger.log_message("✅ 退出登录成功（重定向）", "INFO")
                return True
            elif response.status_code == 403 and response.headers.get('X-LuCI-Login-Required') == 'yes':
                # 403但要求登录，说明session已失效，这也算是"退出"成功
                print(f"✅ 会话已失效（需要重新登录）")
                print(f"💡 X-LuCI-Login-Required: yes - session已过期")
                request_logger.log_message("✅ 会话已失效，无需退出", "INFO")
                return True
            else:
                print(f"❌ 退出登录失败，状态码: {response.status_code}")
                print(f"🔍 响应头: {dict(response.headers)}")
                print(f"🔍 响应内容前200字符: {response.text[:200]}")
                request_logger.log_message(f"❌ 退出登录失败，状态码: {response.status_code}", "ERROR")
                return False

        except Exception as e:
            print(f"❌ 退出登录失败: {e}")
            request_logger.log_error(e, "退出登录过程中发生异常")
            return False

    def create_empty_socks_config(self, node):
        """创建空的Socks配置，返回配置ID"""
        try:
            # 步骤1: 添加Socks配置
            add_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2/settings"

            # 使用multipart/form-data格式
            add_files = {
                'token': (None, self.token),
                'cbi.submit': (None, '1'),
                'cbi.cts.passwall2.socks.': (None, '添加'),
                'cbi.apply': (None, '1')
            }

            # 保存第一步请求包（可选）
            if self.save_create_files:
                self._save_create_step1_request_packet(add_url, add_files, node)

            # 构建完整的请求头，与日志文件中的格式一致
            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Encoding': 'gzip, deflate',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'max-age=0',
                'Origin': self.host,
                'Referer': add_url,
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            # 构建multipart/form-data请求体，与日志文件格式完全一致
            import requests_toolbelt
            from requests_toolbelt.multipart.encoder import MultipartEncoder

            multipart_data = MultipartEncoder(
                fields=add_files,
                boundary='----WebKitFormBoundaryhabCBqgi2aWqRIHt'
            )

            headers['Content-Type'] = multipart_data.content_type

            response = self.session.post(add_url, data=multipart_data, headers=headers, allow_redirects=False)

            # 保存第一步响应包（可选）
            if self.save_create_files:
                self._save_create_step1_response_packet(response, node)

            if response.status_code != 302:
                print(f"❌ 创建空配置失败: {node['name']}")
                return None

            # 从重定向URL中提取新配置的ID
            location = response.headers.get('Location', '')
            config_id_match = re.search(r'/socks_config/([^/]+)$', location)
            if not config_id_match:
                print(f"❌ 无法提取配置ID: {node['name']}")
                return None

            config_id = config_id_match.group(1)
            print(f"✅ 创建空配置成功: {node['name']} -> ID: {config_id}")
            request_logger.log_message(f"✅ 创建空配置成功: {node['name']} -> ID: {config_id}", "SUCCESS")

            return config_id

        except Exception as e:
            print(f"❌ 创建空配置失败: {node['name']} - {e}")
            request_logger.log_error(e, f"创建空配置过程中发生异常: {node['name']}")
            return None

    def create_socks_config(self, node, port):
        """创建新的Socks配置"""
        try:
            # 步骤1: 添加Socks配置
            add_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2/settings"

            # 使用multipart/form-data格式
            add_files = {
                'token': (None, self.token),
                'cbi.submit': (None, '1'),
                'cbi.cts.passwall2.socks.': (None, '添加'),
                'cbi.apply': (None, '1')
            }

            # 保存第一步请求包（可选）
            if self.save_create_files:
                self._save_create_step1_request_packet(add_url, add_files, node)

            # 构建完整的请求头，与日志文件中的格式一致
            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Encoding': 'gzip, deflate',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'max-age=0',
                'Origin': self.host,
                'Referer': add_url,
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            # 构建multipart/form-data请求体，与日志文件格式完全一致
            import requests_toolbelt
            from requests_toolbelt.multipart.encoder import MultipartEncoder

            multipart_data = MultipartEncoder(
                fields=add_files,
                boundary='----WebKitFormBoundaryhabCBqgi2aWqRIHt'
            )

            headers['Content-Type'] = multipart_data.content_type

            response = self.session.post(add_url, data=multipart_data, headers=headers, allow_redirects=False)

            # 保存第一步响应包（可选）
            if self.save_create_files:
                self._save_create_step1_response_packet(response, node)
            
            if response.status_code != 302:
                return False
                
            # 从重定向URL中提取新配置的ID
            location = response.headers.get('Location', '')
            config_id_match = re.search(r'/socks_config/([^/]+)$', location)
            if not config_id_match:
                return False
                
            config_id = config_id_match.group(1)
            print(f"✅ 成功创建Socks配置条目，ID: {config_id}")
            request_logger.log_message(f"✅ 成功创建Socks配置条目，ID: {config_id}", "SUCCESS")

            # 步骤2: 获取专用token和sessionid
            config_url = f"{self.host}/cgi-bin/luci/admin/services/passwall2/socks_config/{config_id}"
            config_token, config_sessionid = self._get_config_token_and_sessionid(config_url, node, config_id)

            if not config_token or not config_sessionid:
                print(f"❌ 获取配置页面token和sessionid失败")
                request_logger.log_message(f"❌ 获取配置页面token和sessionid失败", "ERROR")
                return False

            print(f"🔑 获取到配置专用token: {config_token}")
            print(f"🔑 获取到配置专用sessionid: {config_sessionid}")
            request_logger.log_message(f"🔑 获取到配置专用token: {config_token}", "SUCCESS")
            request_logger.log_message(f"🔑 获取到配置专用sessionid: {config_sessionid}", "SUCCESS")

            # 步骤3: 配置Socks参数

            # 使用multipart/form-data格式，使用专用token和sessionid
            config_files = {
                'token': (None, config_token),  # 使用专用token
                'sessionid': (None, config_sessionid),  # 添加sessionid
                'cbi.submit': (None, '1'),
                'cbi.cbe.passwall2.' + config_id + '.enabled': (None, '1'),
                'cbid.passwall2.' + config_id + '.enabled': (None, '1'),
                'cbid.passwall2.' + config_id + '.node': (None, node['id']),
                'cbi.cbe.passwall2.' + config_id + '.bind_local': (None, '1'),
                'cbid.passwall2.' + config_id + '.port': (None, str(port)),
                'cbid.passwall2.' + config_id + '.http_port': (None, '0'),
                'cbi.cbe.passwall2.' + config_id + '.log': (None, '1'),
                'cbid.passwall2.' + config_id + '.log': (None, '1'),
                'cbi.cbe.passwall2.' + config_id + '.enable_autoswitch': (None, '1'),
                'cbi.apply': (None, '1')  # 恢复 cbi.apply 字段
            }

            # 保存第二步请求包（可选）
            if self.save_create_files:
                self._save_create_step2_request_packet(config_url, config_files, node, config_id, port)

            # 构建完整的请求头，与日志文件中的格式一致
            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Encoding': 'gzip, deflate',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'max-age=0',
                'Origin': self.host,
                'Referer': config_url,
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            # 构建multipart/form-data请求体，与日志文件格式完全一致
            multipart_data = MultipartEncoder(
                fields=config_files,
                boundary='----WebKitFormBoundaryhabCBqgi2aWqRIHt'
            )

            headers['Content-Type'] = multipart_data.content_type

            response = self.session.post(config_url, data=multipart_data, headers=headers)

            # 保存第二步响应包（可选）
            if self.save_create_files:
                self._save_create_step2_response_packet(response, node, config_id)

            success = response.status_code == 200 or response.status_code == 302
            if success:
                print(f"✅ 成功配置Socks参数: {node['name']} -> 端口 {port}")
                request_logger.log_message(f"✅ 成功配置Socks参数: {node['name']} -> 端口 {port}", "SUCCESS")
            else:
                print(f"❌ 配置Socks参数失败: {node['name']}")
                request_logger.log_message(f"❌ 配置Socks参数失败: {node['name']}", "ERROR")

            return success
            
        except Exception as e:
            print(f"创建Socks配置失败: {e}")
            request_logger.log_error(e, "创建Socks配置过程中发生异常")
            return False

    def _save_create_step1_request_packet(self, url, files_data, node):
        """保存创建步骤1的请求包"""
        try:
            import datetime
            from urllib.parse import urlparse

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_name = "".join(c for c in node['name'] if c.isalnum() or c in (' ', '-', '_')).rstrip()[:20]
            filename = f"创建请求包1_{safe_name}_{timestamp}.txt"

            parsed_url = urlparse(url)
            boundary = "----WebKitFormBoundarybXIRhYd7tS6iobjo"

            # 构建multipart/form-data请求体
            body_parts = []
            for key, (filename_part, value) in files_data.items():
                part = f"------WebKitFormBoundarybXIRhYd7tS6iobjo\n"
                part += f'Content-Disposition: form-data; name="{key}"\n\n'
                part += f"{value}\n"
                body_parts.append(part)

            body_parts.append(f"------WebKitFormBoundarybXIRhYd7tS6iobjo--\n")
            request_body = "".join(body_parts)
            content_length = len(request_body.encode('utf-8'))

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("提交包：\n")
                f.write(f"POST {parsed_url.path} HTTP/1.1\n")
                f.write("Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\n")
                f.write("Accept-Encoding: gzip, deflate\n")
                f.write("Accept-Language: zh-CN,zh;q=0.9\n")
                f.write("Cache-Control: max-age=0\n")
                f.write(f"Content-Length: {content_length}\n")
                f.write(f"Content-Type: multipart/form-data; boundary={boundary}\n")

                # Cookie
                for cookie in self.session.cookies:
                    if cookie.name == 'sysauth_http':
                        f.write(f"Cookie: sysauth_http={cookie.value}\n")
                        break

                f.write(f"Host: {parsed_url.netloc}\n")
                f.write(f"Origin: {parsed_url.scheme}://{parsed_url.netloc}\n")
                f.write("Proxy-Connection: keep-alive\n")
                f.write(f"Referer: {url}\n")
                f.write("Upgrade-Insecure-Requests: 1\n")
                f.write("User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\n")
                f.write("\n")
                f.write(request_body)

            print(f"💾 创建步骤1请求包已保存到: {filename}")
            request_logger.log_message(f"💾 创建步骤1请求包已保存到: {filename}", "INFO")

        except Exception as e:
            print(f"❌ 保存创建步骤1请求包失败: {e}")

    def _save_create_step1_response_packet(self, response, node):
        """保存创建步骤1的响应包"""
        try:
            import datetime

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_name = "".join(c for c in node['name'] if c.isalnum() or c in (' ', '-', '_')).rstrip()[:20]
            filename = f"创建响应包1_{safe_name}_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("创建步骤1响应结果:\n")
                f.write("=" * 30 + "\n")
                f.write(f"状态码: {response.status_code}\n")
                f.write(f"状态: {response.reason}\n")
                if response.status_code == 302:
                    location = response.headers.get('Location', '')
                    f.write(f"重定向地址: {location}\n")
                f.write("=" * 30 + "\n")
                f.write("响应内容:\n")
                f.write(response.text)

            print(f"💾 创建步骤1响应包已保存到: {filename}")

        except Exception as e:
            print(f"❌ 保存创建步骤1响应包失败: {e}")

    def _save_create_step2_request_packet(self, url, files_data, node, config_id, port):
        """保存创建步骤2的请求包"""
        try:
            import datetime
            from urllib.parse import urlparse

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_name = "".join(c for c in node['name'] if c.isalnum() or c in (' ', '-', '_')).rstrip()[:20]
            filename = f"创建请求包2_{safe_name}_{timestamp}.txt"

            parsed_url = urlparse(url)
            boundary = "----WebKitFormBoundaryhabCBqgi2aWqRIHt"

            # 构建multipart/form-data请求体
            body_parts = []
            for key, (filename_part, value) in files_data.items():
                part = f"------WebKitFormBoundaryhabCBqgi2aWqRIHt\n"
                part += f'Content-Disposition: form-data; name="{key}"\n\n'
                part += f"{value}\n"
                body_parts.append(part)

            body_parts.append(f"------WebKitFormBoundaryhabCBqgi2aWqRIHt--\n")
            request_body = "".join(body_parts)
            content_length = len(request_body.encode('utf-8'))

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("提交包：\n")
                f.write(f"POST {parsed_url.path} HTTP/1.1\n")
                f.write("Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\n")
                f.write("Accept-Encoding: gzip, deflate\n")
                f.write("Accept-Language: zh-CN,zh;q=0.9\n")
                f.write("Cache-Control: max-age=0\n")
                f.write(f"Content-Length: {content_length}\n")
                f.write(f"Content-Type: multipart/form-data; boundary={boundary}\n")

                # Cookie
                for cookie in self.session.cookies:
                    if cookie.name == 'sysauth_http':
                        f.write(f"Cookie: sysauth_http={cookie.value}\n")
                        break

                f.write(f"Host: {parsed_url.netloc}\n")
                f.write(f"Origin: {parsed_url.scheme}://{parsed_url.netloc}\n")
                f.write("Proxy-Connection: keep-alive\n")
                f.write(f"Referer: {url}\n")
                f.write("Upgrade-Insecure-Requests: 1\n")
                f.write("User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\n")
                f.write("\n")
                f.write(request_body)

            print(f"💾 创建步骤2请求包已保存到: {filename}")
            request_logger.log_message(f"💾 创建步骤2请求包已保存到: {filename}", "INFO")

        except Exception as e:
            print(f"❌ 保存创建步骤2请求包失败: {e}")

    def _save_create_step2_response_packet(self, response, node, config_id):
        """保存创建步骤2的响应包"""
        try:
            import datetime

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_name = "".join(c for c in node['name'] if c.isalnum() or c in (' ', '-', '_')).rstrip()[:20]
            filename = f"创建响应包2_{safe_name}_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("创建步骤2响应结果:\n")
                f.write("=" * 30 + "\n")
                f.write(f"配置ID: {config_id}\n")
                f.write(f"状态码: {response.status_code}\n")
                f.write(f"状态: {response.reason}\n")
                f.write("=" * 30 + "\n")
                f.write("响应内容:\n")
                f.write(response.text)

            print(f"💾 创建步骤2响应包已保存到: {filename}")

        except Exception as e:
            print(f"❌ 保存创建步骤2响应包失败: {e}")

    def _get_config_token_and_sessionid(self, config_url, node, config_id):
        """获取配置页面的专用token和sessionid"""
        try:
            import time
            import re

            # 添加时间戳防止缓存
            timestamp = int(time.time() * 1000)

            print(f"🔍 获取配置页面token和sessionid: {config_url}")
            request_logger.log_message(f"🔍 获取配置页面token和sessionid: {config_url}", "INFO")

            # 记录GET请求
            request_logger.log_request("GET", config_url, headers=dict(self.session.headers))

            # GET访问配置页面，携带cookies和防缓存参数
            response = self.session.get(config_url, params={'_': timestamp})

            # 记录响应
            request_logger.log_response(response)

            # 保存配置页面响应（可选）
            if self.save_create_files:
                self._save_config_page_response(response, node, config_id)

            if response.status_code == 200:
                # 从HTML中解析token和sessionid
                # 查找类似："],"sessionid":"8227db27c31beba321bb6907611a76f2","token":"74d08e800aa3a0105e2bec908521c8e3","
                pattern = r'"sessionid":"([^"]+)","token":"([^"]+)"'
                match = re.search(pattern, response.text)

                if match:
                    sessionid = match.group(1)
                    token = match.group(2)

                    print(f"✅ 成功解析token: {token}")
                    print(f"✅ 成功解析sessionid: {sessionid}")
                    request_logger.log_message(f"✅ 成功解析token: {token}", "SUCCESS")
                    request_logger.log_message(f"✅ 成功解析sessionid: {sessionid}", "SUCCESS")

                    return token, sessionid
                else:
                    print(f"❌ 未找到token和sessionid模式")
                    request_logger.log_message(f"❌ 未找到token和sessionid模式", "ERROR")
                    return None, None
            else:
                print(f"❌ 访问配置页面失败，状态码: {response.status_code}")
                request_logger.log_message(f"❌ 访问配置页面失败，状态码: {response.status_code}", "ERROR")
                return None, None

        except Exception as e:
            print(f"❌ 获取配置页面token和sessionid异常: {e}")
            request_logger.log_error(e, "获取配置页面token和sessionid过程中发生异常")
            return None, None

    def _save_config_page_response(self, response, node, config_id):
        """保存配置页面响应到本地文件"""
        try:
            import datetime

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_name = "".join(c for c in node['name'] if c.isalnum() or c in (' ', '-', '_')).rstrip()[:20]
            filename = f"配置页面响应_{safe_name}_{config_id}_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("配置页面响应结果:\n")
                f.write("=" * 50 + "\n")
                f.write(f"节点名称: {node['name']}\n")
                f.write(f"配置ID: {config_id}\n")
                f.write(f"状态码: {response.status_code}\n")
                f.write(f"状态: {response.reason}\n")
                f.write("=" * 50 + "\n")
                f.write("响应内容:\n")
                f.write(response.text)

            print(f"💾 配置页面响应已保存到: {filename}")
            request_logger.log_message(f"💾 配置页面响应已保存到: {filename}", "INFO")

        except Exception as e:
            print(f"❌ 保存配置页面响应失败: {e}")

    def _save_actual_request_packet(self, request, node, prefix):
        """保存实际发送的请求包"""
        try:
            import datetime
            from urllib.parse import urlparse

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_name = "".join(c for c in node['name'] if c.isalnum() or c in (' ', '-', '_')).rstrip()[:20]
            filename = f"{prefix}_{safe_name}_{timestamp}.txt"

            parsed_url = urlparse(request.url)

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("实际发送的请求包：\n")
                f.write("=" * 50 + "\n")
                f.write(f"{request.method} {parsed_url.path}")
                if parsed_url.query:
                    f.write(f"?{parsed_url.query}")
                f.write(" HTTP/1.1\n")

                # 写入实际的请求头
                for header_name, header_value in request.headers.items():
                    f.write(f"{header_name}: {header_value}\n")

                f.write("\n")

                # 写入实际的请求体
                if request.body:
                    if isinstance(request.body, bytes):
                        try:
                            body_str = request.body.decode('utf-8')
                        except UnicodeDecodeError:
                            body_str = str(request.body)
                    else:
                        body_str = str(request.body)
                    f.write(body_str)
                else:
                    f.write("(无请求体)")

            print(f"💾 实际请求包已保存到: {filename}")
            request_logger.log_message(f"💾 实际请求包已保存到: {filename}", "INFO")

        except Exception as e:
            print(f"❌ 保存实际请求包失败: {e}")

    def get_port_for_region(self, region):
        """根据地区获取端口号"""
        region_ports = Config.REGION_PORTS

        # 查找匹配的地区
        for region_key, base_port in region_ports.items():
            if region_key in region:
                # 使用基础端口，可以根据需要添加递增逻辑
                return base_port

        # 默认端口
        return Config.DEFAULT_PORT

    def get_next_available_port(self, region, existing_ports=None):
        """获取地区的下一个可用端口"""
        if existing_ports is None:
            existing_ports = set()

        base_port = self.get_port_for_region(region)

        # 从基础端口开始查找可用端口
        port = base_port
        while port in existing_ports:
            port += 1
            # 避免端口号过大
            if port > base_port + 1000:
                port = base_port
                break

        return port
