#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
包含应用程序的各种配置参数
"""

class Config:
    # 默认连接参数（从登录包.txt提取）
    DEFAULT_HOST = "*************"
    DEFAULT_USERNAME = "root"
    DEFAULT_PASSWORD = "password"
    
    # 地区端口映射
    REGION_PORTS = {
        "香港": 10000,
        "美国": 20000,
        "日本": 30000,
        "新加坡": 40000,
        "韩国": 50000,
        "台湾": 60000,
        "英国": 70000,
        "德国": 80000,
        "法国": 90000,
        "加拿大": 11000,
        "澳大利亚": 12000,
        "荷兰": 13000,
        "瑞士": 14000,
        "俄罗斯": 15000,
        "印度": 16000,
        "泰国": 17000,
        "马来西亚": 18000,
        "菲律宾": 19000,
        "越南": 21000,
        "印尼": 22000,
    }
    
    # 默认端口（未知地区使用）
    DEFAULT_PORT = 1080
    
    # 请求超时时间（秒）
    REQUEST_TIMEOUT = 30
    
    # 请求间隔时间（秒）
    REQUEST_INTERVAL = 0.5
    
    # 最大重试次数
    MAX_RETRIES = 3

# 为了兼容性，创建REGION_PORT_MAP别名
REGION_PORT_MAP = Config.REGION_PORTS
