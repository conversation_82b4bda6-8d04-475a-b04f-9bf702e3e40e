OpenWrt Passwall 工具运行日志
==================================================

[2025-08-04 12:38:11] [INFO] 开始登录OpenWrt后台

[2025-08-04 12:38:11] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
POST数据:
  luci_username: root
  luci_password: ********
------------------------------
[2025-08-04 12:38:11] === HTTP响应 登录请求 ===
状态码: 302
状态文本: Found
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  Set-Cookie: sysauth_http=f187eeb1d865e37a565ceb081a134842; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  Location: /cgi-bin/luci/

🍪 重要Cookie信息:
  完整Cookie: sysauth_http=f187eeb1d865e37a565ceb081a134842; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  ✅ 提取到sysauth_http: f187eeb1d865e37a565ceb081a134842

响应内容:

==================================================

[2025-08-04 12:38:11] [SUCCESS] ✅ 登录成功！Session ID: f187eeb1d865e37a565ceb081a134842

[2025-08-04 12:38:11] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/node_list
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-04 12:38:13] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Node List
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touch-ico...
==================================================

[2025-08-04 12:38:13] [SUCCESS] ✅ 从节点列表页面成功获取到 135 个节点

[2025-08-04 12:38:14] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/settings
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-04 12:38:14] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Basic Settings
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touc...
==================================================

[2025-08-04 12:38:14] [SUCCESS] 🔑 成功提取token: ded2e3a33773d5d389c8a15c07e89caf
[2025-08-04 12:38:14] [SUCCESS] 📋 已存在的socks配置: ['5x7DWkGi', 't72DK2Xi', 'tLkGzYbz', 'Fx7XAtT3', 'HruEVKwt', 'zRijuLon', 'HgPastyX', 'SvL6RxX2', 'ApeXo2se', 'YVVH5Mu6', 'Ppm6FEG7', 'XSmy5RrU', 'P7wim9YC', '6p8ANW9y', 'AOflCmFO', 'JG6WoEZE', 'gkzZbWRG', '2oD2EOOO', 'VKbznCDK', 'BvWILhHL', 'o6mtMUjk', 'owFzrN2A', '550DSM2k', 'V42xvl1L', 'TW1WruHn', 'OCrqVNxo', 'uaQpjYck', 'agVF4udk', 'Rb3TxULw', 'i1OCLuIX', 'xiznzlO3', '0UFJaJAC', 'rh3EhDdq', '8tw15ymJ', 'q0kVvUBj', 'F8rliDad', 'AF6kck72', 'ahEOBs9D', 'lWO8YmR1', '3gOkwKrx', 'jtwDji14', 'Bc2gdhzk', '2TUAHFvd', 'DWHaDqCt', 'z83xEAGE', 'OLx141wU', 'wnKoLnAL', 'RHILFSc0', 'NkCVw21K', 'dfKJRGXA', 'NNlpDOHi', 'W3uocwfm', 'w7BaGIe7', 'NJ9vkidq', 'ZhEOwopf', 'Kgn4qzTS', '1cTrQURu', 'mWIMtGPy', 'd2E3uxu6', '326wk3vl', 'AqRgVTNs', 'SdRd9ACB', 'C2Qnsss9', 'EsyjStUg', 'rzXsXi6U', 'F37CSPgB', 'kFATD6fE', 'bGaXhQAD', 'RQzcwk9g', 'HuOjfFwg', 's0eodQKc', '0pwUyPr7', 'QvLkvMNm', 'Uu2Uv7Oj', '3EnP1BQA', 'dR5pdyCU', 'OpqhMdyE', 'hoC1OnpB', 'eFsq1w8u', 'btq5B8gI', 'U4tbrZbd', 'AK538WDd', 'CnAgw6uz', 'qfhEVyUS', 'wxDlxuKZ', 'm9jengSG', 'DR7DymlB', 'BeqJmUFi', 'he0m3hW9', 'yrSuCOLO', '3J8m91qX', 'VCQpxCNO', 'LyKbqeBn', 'tth3h32C', 'QSpFGCaW', 'NF9ej4QF', 'zKaDXHwF', 'g51TwItc', 'MDQ3Jea3', 'TAhGVwqM', 'NRTtJpRu', '55dvLfWf', 'eca9n8WA', 'EAPQDV5w', 'MpaXYOvv', 'W8ksWKrO', 'JpLUwyDc', 'OfSqMTbs', 'v90TQ2nS', 'hzqbVtjX', 'l7Kmtv3F', 'IoUQAQSK', 'RqM3qnPX', 'UawWG95o', 'oRVRkFiR', 'DEQDMuRX', 'gzVPhsGM', 'I25IvvDB', 'pvwUt0Mb', 'RwTdW7mu', 'AuBNTTMs', 'cuaSk13D', 'ztdmon86', '481N8dTu', 'cYCE3AVa', 'Nb7bxpRB', 'UDRafMJD', 'uLrqTbzY', '746yuAFv', 'Ts9gBEs9', 'YZgxMWd2', '2Phwovak', '0xrihtnB', 'B391hDYz', '7mZli6Fx']

[2025-08-04 12:38:14] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/admin/services/passwall2/settings
请求头:
  Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
  Accept-Encoding: gzip, deflate
  Accept-Language: zh-CN,zh;q=0.9
  Cache-Control: max-age=0
  Origin: http://*************
  Referer: http://*************/cgi-bin/luci/admin/services/passwall2/settings
  Upgrade-Insecure-Requests: 1
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryACE5azrrYYWFcSp0
POST数据: multipart_data
------------------------------
[2025-08-04 12:38:15] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="zh-cn">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - åºæ¬è®¾ç½®
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-tou...
==================================================

[2025-08-04 12:38:15] [SUCCESS] ✅ 删除socks配置请求成功
[2025-08-04 12:38:15] [INFO] 💾 已更新nodes_data.json，共 135 个节点
[2025-08-04 12:38:15] [SUCCESS] ✅ 创建空配置成功: Hong Kong 01 -> ID: OODJHkc8
[2025-08-04 12:38:16] [SUCCESS] ✅ 创建空配置成功: Hong Kong 02 -> ID: 7MvWVHUN
[2025-08-04 12:38:16] [SUCCESS] ✅ 创建空配置成功: Hong Kong 03 -> ID: KO7cDfqu
[2025-08-04 12:38:16] [SUCCESS] ✅ 创建空配置成功: Hong Kong 04 -> ID: KfdtRFrz
[2025-08-04 12:38:17] [SUCCESS] ✅ 创建空配置成功: Hong Kong 05 -> ID: 6UXDURmQ
[2025-08-04 12:38:17] [SUCCESS] ✅ 创建空配置成功: Hong Kong 06 -> ID: L82rskVh
[2025-08-04 12:38:17] [SUCCESS] ✅ 创建空配置成功: Hong Kong 07 -> ID: wuJnFrY1
[2025-08-04 12:38:18] [SUCCESS] ✅ 创建空配置成功: Hong Kong 08 -> ID: wm0o3c2y
[2025-08-04 12:38:18] [SUCCESS] ✅ 创建空配置成功: Hong Kong 09 -> ID: faA6JVOW
[2025-08-04 12:38:19] [SUCCESS] ✅ 创建空配置成功: Hong Kong 10 -> ID: 14IWgUBu
[2025-08-04 12:38:19] [SUCCESS] ✅ 创建空配置成功: Hong Kong 11 -> ID: XlAHeCKu
[2025-08-04 12:38:20] [SUCCESS] ✅ 创建空配置成功: Hong Kong 12 -> ID: nyMm7heg
[2025-08-04 12:38:21] [SUCCESS] ✅ 创建空配置成功: Hong Kong 13 -> ID: 69CSD3GY
[2025-08-04 12:38:21] [SUCCESS] ✅ 创建空配置成功: Hong Kong 14 -> ID: Uh9J9OVm
[2025-08-04 12:38:22] [SUCCESS] ✅ 创建空配置成功: Hong Kong 15 -> ID: b4aionGy
[2025-08-04 12:38:23] [SUCCESS] ✅ 创建空配置成功: Hong Kong 16 -> ID: j2RFDukX
[2025-08-04 12:38:24] [SUCCESS] ✅ 创建空配置成功: Hong Kong 17 -> ID: fsyh6FZf
[2025-08-04 12:38:24] [SUCCESS] ✅ 创建空配置成功: Hong Kong 18 -> ID: uDKcGklq
[2025-08-04 12:38:25] [SUCCESS] ✅ 创建空配置成功: Hong Kong 19 -> ID: yIQ5y8qz
[2025-08-04 12:38:26] [SUCCESS] ✅ 创建空配置成功: Hong Kong 20 [Premium] -> ID: G5KOvq1a
[2025-08-04 12:38:27] [SUCCESS] ✅ 创建空配置成功: Hong Kong 21 [Premium] -> ID: nkWGqA2U
[2025-08-04 12:38:28] [SUCCESS] ✅ 创建空配置成功: Hong Kong 22 [Premium] -> ID: SIhMGfAS
[2025-08-04 12:38:29] [SUCCESS] ✅ 创建空配置成功: Hong Kong 23 -> ID: jaDc1R7E
[2025-08-04 12:38:30] [SUCCESS] ✅ 创建空配置成功: USA Seattle 01 -> ID: ZIS9rhQK
[2025-08-04 12:38:31] [SUCCESS] ✅ 创建空配置成功: USA Seattle 02 -> ID: iupWS6Cl
[2025-08-04 12:38:32] [SUCCESS] ✅ 创建空配置成功: USA Seattle 03 -> ID: xDQeARfw
[2025-08-04 12:38:33] [SUCCESS] ✅ 创建空配置成功: USA Seattle 04 -> ID: oEJQYSpi
[2025-08-04 12:38:34] [SUCCESS] ✅ 创建空配置成功: USA Seattle 05 -> ID: D1TPyCGR
[2025-08-04 12:38:36] [SUCCESS] ✅ 创建空配置成功: USA Seattle 06 -> ID: 7LKb4nd1
[2025-08-04 12:38:37] [SUCCESS] ✅ 创建空配置成功: USA Seattle 07 -> ID: IJdycLb8
[2025-08-04 12:38:38] [SUCCESS] ✅ 创建空配置成功: USA Seattle 08 -> ID: 9knWgU54
[2025-08-04 12:38:39] [SUCCESS] ✅ 创建空配置成功: USA Seattle 09 -> ID: pxa1TEFr
[2025-08-04 12:38:41] [SUCCESS] ✅ 创建空配置成功: USA San Jose 01 [Premium] -> ID: VRno7smz
[2025-08-04 12:38:42] [SUCCESS] ✅ 创建空配置成功: USA San Jose 02 [Premium] -> ID: CTkxRJjo
[2025-08-04 12:38:44] [SUCCESS] ✅ 创建空配置成功: USA San Jose 03 [Premium] -> ID: W6dr0UUh
[2025-08-04 12:38:45] [SUCCESS] ✅ 创建空配置成功: USA San Jose 04 [Premium] -> ID: VfZsITX8
[2025-08-04 12:38:46] [SUCCESS] ✅ 创建空配置成功: USA San Jose 05 [Premium] -> ID: difRKTCg
[2025-08-04 12:38:48] [SUCCESS] ✅ 创建空配置成功: USA San Jose 06 [Premium] -> ID: 2lx27xxE
[2025-08-04 12:38:50] [SUCCESS] ✅ 创建空配置成功: USA San Jose 07 -> ID: 5hHBV6xQ
[2025-08-04 12:38:51] [SUCCESS] ✅ 创建空配置成功: USA San Jose 08 -> ID: 3oWkxRiM
[2025-08-04 12:38:53] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 01 -> ID: Xllt215h
[2025-08-04 12:38:55] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 02 -> ID: 9z65biNQ
[2025-08-04 12:38:56] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 03 -> ID: GReTymqu
[2025-08-04 12:38:58] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 04 -> ID: 1OXx0kFx
[2025-08-04 12:39:00] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 05 -> ID: YycOoKK4
[2025-08-04 12:39:02] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 06 -> ID: 3Vgs0AHM
[2025-08-04 12:39:04] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 07 -> ID: eOe86D8g
[2025-08-04 12:39:06] [SUCCESS] ✅ 创建空配置成功: Russia St. Petersburg -> ID: 8RTpxAuP
[2025-08-04 12:39:08] [SUCCESS] ✅ 创建空配置成功: Russia Moscow 01 -> ID: R4qkCp54
[2025-08-04 12:39:10] [SUCCESS] ✅ 创建空配置成功: Austria 01 -> ID: m3KHk2AW
[2025-08-04 12:39:12] [SUCCESS] ✅ 创建空配置成功: Australia Sydney 01 [Premium] -> ID: C7qRsoem
[2025-08-04 12:39:14] [SUCCESS] ✅ 创建空配置成功: Australia Sydney 02 -> ID: 1cgsY2Zw
[2025-08-04 12:39:16] [SUCCESS] ✅ 创建空配置成功: Japan 01 -> ID: DvkllZsx
[2025-08-04 12:39:18] [SUCCESS] ✅ 创建空配置成功: Japan 02 -> ID: Im2LO3Y2
[2025-08-04 12:39:21] [SUCCESS] ✅ 创建空配置成功: Japan 03 -> ID: d32ZVAUo
[2025-08-04 12:39:23] [SUCCESS] ✅ 创建空配置成功: Japan 04 -> ID: ydyHChif
[2025-08-04 12:39:25] [SUCCESS] ✅ 创建空配置成功: Japan 05 -> ID: Ue2lTIoy
[2025-08-04 12:39:28] [SUCCESS] ✅ 创建空配置成功: Japan 06 -> ID: fNKvdDnk
[2025-08-04 12:39:30] [SUCCESS] ✅ 创建空配置成功: Japan 07 -> ID: RvwhgJ4J
[2025-08-04 12:39:33] [SUCCESS] ✅ 创建空配置成功: Japan 08 -> ID: POABOZwO
[2025-08-04 12:39:35] [SUCCESS] ✅ 创建空配置成功: Japan 09 -> ID: s0DLhhfF
[2025-08-04 12:39:38] [SUCCESS] ✅ 创建空配置成功: Japan 10 -> ID: rJPP16m9
[2025-08-04 12:39:40] [SUCCESS] ✅ 创建空配置成功: Japan 11 -> ID: S5hKgE5r
[2025-08-04 12:39:43] [SUCCESS] ✅ 创建空配置成功: Japan 12 -> ID: GynVn2ER
[2025-08-04 12:39:46] [SUCCESS] ✅ 创建空配置成功: Japan 13 -> ID: ueles8w3
[2025-08-04 12:39:48] [SUCCESS] ✅ 创建空配置成功: Japan 14 -> ID: 7XOcHgmA
[2025-08-04 12:39:51] [SUCCESS] ✅ 创建空配置成功: Japan 15 -> ID: nATqrDoR
[2025-08-04 12:39:54] [SUCCESS] ✅ 创建空配置成功: Japan 16 -> ID: HyC1qOPj
[2025-08-04 12:39:56] [INFO] 🔍 找到主配置ID: cfg013fd6
[2025-08-04 12:39:56] [INFO] 🔑 更新token: ded2e3a33773d5d389c8a15c07e89caf -> ded2e3a33773d5d389c8a15c07e89caf
[2025-08-04 12:39:56] [INFO] 🔑 更新sessionid: f187eeb1d865e37a565ceb081a134842 -> f187eeb1d865e37a565ceb081a134842
[2025-08-04 12:39:56] [INFO] 💾 批量提交请求包已保存到: 批量提交请求包_20250804_123956.txt
[2025-08-04 12:39:59] [INFO] 💾 批量提交响应包已保存到: 批量提交响应包_20250804_123959.txt
[2025-08-04 12:39:59] [SUCCESS] ✅ 批量提交成功！已设置 135 个配置
[2025-08-04 12:39:59] [INFO] ✅ 会话已失效，无需退出
[2025-08-04 12:39:59] [INFO] 开始登录OpenWrt后台

[2025-08-04 12:39:59] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
POST数据:
  luci_username: root
  luci_password: ********
------------------------------
[2025-08-04 12:39:59] === HTTP响应 登录请求 ===
状态码: 302
状态文本: Found
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  Set-Cookie: sysauth_http=01fea33c3b3124d2881dd73007f46be4; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  Location: /cgi-bin/luci/

🍪 重要Cookie信息:
  完整Cookie: sysauth_http=01fea33c3b3124d2881dd73007f46be4; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  ✅ 提取到sysauth_http: 01fea33c3b3124d2881dd73007f46be4

响应内容:

==================================================

[2025-08-04 12:39:59] [SUCCESS] ✅ 登录成功！Session ID: 01fea33c3b3124d2881dd73007f46be4
[2025-08-04 12:40:00] [INFO] 🔍 找到主配置ID: cfg013fd6
[2025-08-04 12:40:00] [INFO] 🔑 更新token: ded2e3a33773d5d389c8a15c07e89caf -> 956febdf881bac29c119bb6ff890b023
[2025-08-04 12:40:00] [INFO] 🔑 更新sessionid: 01fea33c3b3124d2881dd73007f46be4 -> 01fea33c3b3124d2881dd73007f46be4
[2025-08-04 12:40:00] [INFO] 💾 批量提交请求包已保存到: 批量提交请求包_20250804_124000.txt
[2025-08-04 12:40:03] [INFO] 💾 批量提交响应包已保存到: 批量提交响应包_20250804_124003.txt
[2025-08-04 12:40:03] [SUCCESS] ✅ 批量提交成功！已设置 135 个配置
