OpenWrt Passwall 工具运行日志
==================================================

[2025-08-04 13:10:25] [INFO] 开始登录OpenWrt后台

[2025-08-04 13:10:25] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
POST数据:
  luci_username: root
  luci_password: ********
------------------------------
[2025-08-04 13:10:25] === HTTP响应 登录请求 ===
状态码: 302
状态文本: Found
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  Set-Cookie: sysauth_http=c1add2e768458184754726eafe57b953; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  Location: /cgi-bin/luci/

🍪 重要Cookie信息:
  完整Cookie: sysauth_http=c1add2e768458184754726eafe57b953; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  ✅ 提取到sysauth_http: c1add2e768458184754726eafe57b953

响应内容:

==================================================

[2025-08-04 13:10:25] [SUCCESS] ✅ 登录成功！Session ID: c1add2e768458184754726eafe57b953

[2025-08-04 13:10:25] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/node_list
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-04 13:10:27] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Node List
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touch-ico...
==================================================

[2025-08-04 13:10:27] [SUCCESS] ✅ 从节点列表页面成功获取到 135 个节点

[2025-08-04 13:10:27] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/settings
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-04 13:10:27] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Basic Settings
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touc...
==================================================

[2025-08-04 13:10:27] [SUCCESS] 🔑 成功提取token: 12ff5b9365a574fdbff5ac153b3e14d2
[2025-08-04 13:10:27] [SUCCESS] 📋 已存在的socks配置: ['s1deNohp', 'bmyMmlYh']

[2025-08-04 13:10:27] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/admin/services/passwall2/settings
请求头:
  Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
  Accept-Encoding: gzip, deflate
  Accept-Language: zh-CN,zh;q=0.9
  Cache-Control: max-age=0
  Origin: http://*************
  Referer: http://*************/cgi-bin/luci/admin/services/passwall2/settings
  Upgrade-Insecure-Requests: 1
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryACE5azrrYYWFcSp0
POST数据: multipart_data
------------------------------
[2025-08-04 13:10:27] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="zh-cn">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - åºæ¬è®¾ç½®
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-tou...
==================================================

[2025-08-04 13:10:27] [SUCCESS] ✅ 删除socks配置请求成功
[2025-08-04 13:10:27] [INFO] 💾 已更新nodes_data.json，共 1 个节点
[2025-08-04 13:10:27] [SUCCESS] ✅ 创建空配置成功: Hong Kong 01 -> ID: 80PYSCcG
[2025-08-04 13:10:46] [INFO] 🔍 找到主配置ID: cfg013fd6
[2025-08-04 13:10:46] [INFO] 🔑 更新token: 12ff5b9365a574fdbff5ac153b3e14d2 -> 12ff5b9365a574fdbff5ac153b3e14d2
[2025-08-04 13:10:46] [INFO] 🔑 更新sessionid: c1add2e768458184754726eafe57b953 -> c1add2e768458184754726eafe57b953
[2025-08-04 13:10:46] [INFO] 💾 批量提交请求包已保存到: 批量提交请求包_20250804_131046.txt
[2025-08-04 13:10:46] [INFO] 💾 批量提交响应包已保存到: 批量提交响应包_20250804_131046.txt
[2025-08-04 13:10:46] [ERROR] ❌ 批量提交失败，状态码: 403
[2025-08-04 13:10:46] [INFO] ✅ 会话已失效，无需退出
[2025-08-04 13:10:46] [INFO] 开始登录OpenWrt后台

[2025-08-04 13:10:46] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
POST数据:
  luci_username: root
  luci_password: ********
------------------------------
[2025-08-04 13:10:46] === HTTP响应 登录请求 ===
状态码: 302
状态文本: Found
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  Set-Cookie: sysauth_http=8f715576db3558ba13bc9d2860cb8239; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  Location: /cgi-bin/luci/

🍪 重要Cookie信息:
  完整Cookie: sysauth_http=8f715576db3558ba13bc9d2860cb8239; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  ✅ 提取到sysauth_http: 8f715576db3558ba13bc9d2860cb8239

响应内容:

==================================================

[2025-08-04 13:10:46] [SUCCESS] ✅ 登录成功！Session ID: 8f715576db3558ba13bc9d2860cb8239
[2025-08-04 13:10:47] [INFO] 🔍 找到主配置ID: cfg013fd6
[2025-08-04 13:10:47] [INFO] 🔑 更新token: 12ff5b9365a574fdbff5ac153b3e14d2 -> a74e763e250b68478a8fc36d03932f7a
[2025-08-04 13:10:47] [INFO] 🔑 更新sessionid: 8f715576db3558ba13bc9d2860cb8239 -> 8f715576db3558ba13bc9d2860cb8239
[2025-08-04 13:10:47] [INFO] 💾 批量提交请求包已保存到: 批量提交请求包_20250804_131047.txt
[2025-08-04 13:10:47] [INFO] 💾 批量提交响应包已保存到: 批量提交响应包_20250804_131047.txt
[2025-08-04 13:10:47] [ERROR] ❌ 批量提交失败，状态码: 403
