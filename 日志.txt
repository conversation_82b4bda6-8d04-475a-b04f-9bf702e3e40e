OpenWrt Passwall 工具运行日志
==================================================

[2025-08-04 12:20:32] [INFO] 开始登录OpenWrt后台

[2025-08-04 12:20:32] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
POST数据:
  luci_username: root
  luci_password: ********
------------------------------
[2025-08-04 12:20:32] === HTTP响应 登录请求 ===
状态码: 302
状态文本: Found
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  Set-Cookie: sysauth_http=3c908fed0623046b040cbc93b1b1009c; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  Location: /cgi-bin/luci/

🍪 重要Cookie信息:
  完整Cookie: sysauth_http=3c908fed0623046b040cbc93b1b1009c; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  ✅ 提取到sysauth_http: 3c908fed0623046b040cbc93b1b1009c

响应内容:

==================================================

[2025-08-04 12:20:32] [SUCCESS] ✅ 登录成功！Session ID: 3c908fed0623046b040cbc93b1b1009c

[2025-08-04 12:20:32] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/node_list
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-04 12:20:35] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Node List
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touch-ico...
==================================================

[2025-08-04 12:20:35] [SUCCESS] ✅ 从节点列表页面成功获取到 135 个节点

[2025-08-04 12:20:35] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/settings
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-04 12:20:36] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Basic Settings
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touc...
==================================================

[2025-08-04 12:20:36] [SUCCESS] 🔑 成功提取token: bafdd65661920942b73d8c50f0fa85a3
[2025-08-04 12:20:36] [SUCCESS] 📋 已存在的socks配置: ['1pGCDJJ8', 'TiY62osU', 'sJu3NYNC', 'pTmnO5Jl', 'pTgj8nG3', 'izvDIZ4t', '3eXcIB1J', '9HtNsMh6', '8JAvV82N', 'PTsnXQxc', 'VFlFItt1', 'S6Us9z9E', 'YZm28HIt', '9GbQAS7Y', '69JRtyhC', 'z8qpYMAY', 'wKOuGpbk', '53EMeWYY', '1LL4Us2g', 'LNRzBJb4', 'Vrz0iF4i', 'vbJaa3jO', 'KTy3Z4t8', 'ICmTu0od', 'JKUXK0yP', 'FR0hmaUD', '9JzJGkEZ', 'yQDK1k2c', 'YJpPxVyQ', 'tdj8tM93', 'QBd9AZE5', 'Np1DTbov', 'mvBHJNLW', 'njrOm98a', 'P9mbNxGv', 'UQE5SZfp', 'hGGdrowZ', 'AKJkVxc1', 'jp7wwgkD', '1Cr1npPo', 'nHf3Ih4W', 'DsPKaZ9Y', 'NATGJmc3', 'Ly63tnqw', 'aJfu2b0u', 'rHaeBdep', 'AjA7UXSb', 'J1lG2J4d', 'O3NpxHGr', 'DiMfw6qj', 'iaT74yd5', '8QOQwfQI', 'zNqwTGbZ', 'cXk0tI0z', 'hdHyiEuE', 'lS9SPZYy', '1Io3wRET', 'snPf6YcJ', 'jUMjco9F', 'IYISD7Zg', '5mostDKr', 'ttNkrqHF', 'AptikYEr', 'BppuyumM', 'R7ek9Axx', 'IuopgwTo', 'LgtkjJfE', 'TIlMBYtg', 'LPIycC2B', 'Gv4IiQNa', 'PcDDuYkb', 'qv9cwwnb', 'py6wfqEM', 'dO2xxMfw', 'u1TLgSOD', 'Wouwyxpm', 'TQDkCqpg', 'CHpbVj0n', 'OqpiOZ0F', 'iG7CNBFI', 'KXoxtzpy', 'NUoesbi4', 'bk4eLR72', 'VW8QCmxh', '2KxfRuZi', 'EqRTxT3m', 'pFY3Skdl', 'H7oVZL1R', 'jr3E6Y1t', '4TBkQI4z', 'ynUVkM5V', 'jS3bzHV3', '3pyXVU6A', '6B8vMWCp', 'XLQbZlWL', 'xdXW0ssQ', 'BJXtrZPb', 'TTb02S1V', 'vdADUwmn', '5T1PiPXV', 'qK5pWrXv', 'IX8PvlPa', 'WTc9I64Z', 'PtQj2bY5', 'L5TGDSXK', 'sXT1XzBL', 'DPeD7WST', 'HtcGIJhF', 'A9rDhXeU', 'JUaPofVL', 'Kby9Rswf', 'rbAZXdLe', 'pItRa9wB', 'ySAObVsF', 'SwjKVebw', 'gMnJcUAJ', 'Fb0O3fUe', 'UrXkcEsB', 'JX4CsJUP', 'KUgIMFAA', 'bLlCyssn', 'VQdzeC4S', 'EHfqgWzB', 'f6IpMBre', 'lFIDyN0U', 'tiS5UX1h', 'ERwYseFj', '9sShzNbI', 'kiPiULRe', 'H7iCvuQT', 'RoaBq5JM', 'AT8XF62H', 'YZoGPuCT', 'oB94wAE4', 'VOJ9Dh9X']

[2025-08-04 12:20:36] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/admin/services/passwall2/settings
请求头:
  Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
  Accept-Encoding: gzip, deflate
  Accept-Language: zh-CN,zh;q=0.9
  Cache-Control: max-age=0
  Origin: http://*************
  Referer: http://*************/cgi-bin/luci/admin/services/passwall2/settings
  Upgrade-Insecure-Requests: 1
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryACE5azrrYYWFcSp0
POST数据: multipart_data
------------------------------
[2025-08-04 12:20:37] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="zh-cn">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - åºæ¬è®¾ç½®
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-tou...
==================================================

[2025-08-04 12:20:37] [SUCCESS] ✅ 删除socks配置请求成功
[2025-08-04 12:20:37] [INFO] 💾 已更新nodes_data.json，共 135 个节点
[2025-08-04 12:20:37] [SUCCESS] ✅ 创建空配置成功: Hong Kong 01 -> ID: 5x7DWkGi
[2025-08-04 12:20:37] [SUCCESS] ✅ 创建空配置成功: Hong Kong 02 -> ID: tLkGzYbz
[2025-08-04 12:20:38] [SUCCESS] ✅ 创建空配置成功: Hong Kong 03 -> ID: HruEVKwt
[2025-08-04 12:20:38] [SUCCESS] ✅ 创建空配置成功: Hong Kong 04 -> ID: HgPastyX
[2025-08-04 12:20:38] [SUCCESS] ✅ 创建空配置成功: Hong Kong 05 -> ID: ApeXo2se
[2025-08-04 12:20:39] [SUCCESS] ✅ 创建空配置成功: Hong Kong 06 -> ID: Ppm6FEG7
[2025-08-04 12:20:39] [SUCCESS] ✅ 创建空配置成功: Hong Kong 07 -> ID: P7wim9YC
[2025-08-04 12:20:40] [SUCCESS] ✅ 创建空配置成功: Hong Kong 08 -> ID: AOflCmFO
[2025-08-04 12:20:40] [SUCCESS] ✅ 创建空配置成功: Hong Kong 09 -> ID: gkzZbWRG
[2025-08-04 12:20:41] [SUCCESS] ✅ 创建空配置成功: Hong Kong 10 -> ID: VKbznCDK
[2025-08-04 12:20:41] [SUCCESS] ✅ 创建空配置成功: Hong Kong 11 -> ID: o6mtMUjk
[2025-08-04 12:20:42] [SUCCESS] ✅ 创建空配置成功: Hong Kong 12 -> ID: 550DSM2k
[2025-08-04 12:20:42] [SUCCESS] ✅ 创建空配置成功: Hong Kong 13 -> ID: TW1WruHn
[2025-08-04 12:20:43] [SUCCESS] ✅ 创建空配置成功: Hong Kong 14 -> ID: uaQpjYck
[2025-08-04 12:20:44] [SUCCESS] ✅ 创建空配置成功: Hong Kong 15 -> ID: Rb3TxULw
[2025-08-04 12:20:45] [SUCCESS] ✅ 创建空配置成功: Hong Kong 16 -> ID: xiznzlO3
[2025-08-04 12:20:45] [SUCCESS] ✅ 创建空配置成功: Hong Kong 17 -> ID: rh3EhDdq
[2025-08-04 12:20:46] [SUCCESS] ✅ 创建空配置成功: Hong Kong 18 -> ID: q0kVvUBj
[2025-08-04 12:20:47] [SUCCESS] ✅ 创建空配置成功: Hong Kong 19 -> ID: AF6kck72
[2025-08-04 12:20:48] [SUCCESS] ✅ 创建空配置成功: Hong Kong 20 [Premium] -> ID: lWO8YmR1
[2025-08-04 12:20:49] [SUCCESS] ✅ 创建空配置成功: Hong Kong 21 [Premium] -> ID: jtwDji14
[2025-08-04 12:20:50] [SUCCESS] ✅ 创建空配置成功: Hong Kong 22 [Premium] -> ID: 2TUAHFvd
[2025-08-04 12:20:51] [SUCCESS] ✅ 创建空配置成功: Hong Kong 23 -> ID: z83xEAGE
[2025-08-04 12:20:52] [SUCCESS] ✅ 创建空配置成功: Canada 01 -> ID: wnKoLnAL
[2025-08-04 12:20:53] [SUCCESS] ✅ 创建空配置成功: Canada 02 -> ID: NkCVw21K
[2025-08-04 12:20:54] [SUCCESS] ✅ 创建空配置成功: Canada 03 -> ID: NNlpDOHi
[2025-08-04 12:20:55] [SUCCESS] ✅ 创建空配置成功: Netherlands 01 -> ID: w7BaGIe7
[2025-08-04 12:20:56] [SUCCESS] ✅ 创建空配置成功: Switzerland 01 -> ID: ZhEOwopf
[2025-08-04 12:20:58] [SUCCESS] ✅ 创建空配置成功: Switzerland 02 -> ID: 1cTrQURu
[2025-08-04 12:20:59] [SUCCESS] ✅ 创建空配置成功: Chile 01 -> ID: d2E3uxu6
[2025-08-04 12:21:00] [SUCCESS] ✅ 创建空配置成功: India 01 -> ID: AqRgVTNs
[2025-08-04 12:21:01] [SUCCESS] ✅ 创建空配置成功: India 02 -> ID: C2Qnsss9
[2025-08-04 12:21:03] [SUCCESS] ✅ 创建空配置成功: Indonesia 01 -> ID: rzXsXi6U
[2025-08-04 12:21:04] [SUCCESS] ✅ 创建空配置成功: Argentina 01 -> ID: kFATD6fE
[2025-08-04 12:21:06] [SUCCESS] ✅ 创建空配置成功: USA Seattle 01 -> ID: RQzcwk9g
[2025-08-04 12:21:07] [SUCCESS] ✅ 创建空配置成功: USA Seattle 02 -> ID: s0eodQKc
[2025-08-04 12:21:09] [SUCCESS] ✅ 创建空配置成功: USA Seattle 03 -> ID: QvLkvMNm
[2025-08-04 12:21:10] [SUCCESS] ✅ 创建空配置成功: USA Seattle 04 -> ID: 3EnP1BQA
[2025-08-04 12:21:12] [SUCCESS] ✅ 创建空配置成功: USA Seattle 05 -> ID: OpqhMdyE
[2025-08-04 12:21:13] [SUCCESS] ✅ 创建空配置成功: USA Seattle 06 -> ID: eFsq1w8u
[2025-08-04 12:21:15] [SUCCESS] ✅ 创建空配置成功: USA Seattle 07 -> ID: U4tbrZbd
[2025-08-04 12:21:17] [SUCCESS] ✅ 创建空配置成功: USA Seattle 08 -> ID: CnAgw6uz
[2025-08-04 12:21:19] [SUCCESS] ✅ 创建空配置成功: USA Seattle 09 -> ID: wxDlxuKZ
[2025-08-04 12:21:20] [SUCCESS] ✅ 创建空配置成功: USA San Jose 01 [Premium] -> ID: DR7DymlB
[2025-08-04 12:21:22] [SUCCESS] ✅ 创建空配置成功: USA San Jose 02 [Premium] -> ID: he0m3hW9
[2025-08-04 12:21:24] [SUCCESS] ✅ 创建空配置成功: USA San Jose 03 [Premium] -> ID: 3J8m91qX
[2025-08-04 12:21:26] [SUCCESS] ✅ 创建空配置成功: USA San Jose 04 [Premium] -> ID: LyKbqeBn
[2025-08-04 12:21:28] [SUCCESS] ✅ 创建空配置成功: USA San Jose 05 [Premium] -> ID: QSpFGCaW
[2025-08-04 12:21:30] [SUCCESS] ✅ 创建空配置成功: USA San Jose 06 [Premium] -> ID: zKaDXHwF
[2025-08-04 12:21:32] [SUCCESS] ✅ 创建空配置成功: USA San Jose 07 -> ID: MDQ3Jea3
[2025-08-04 12:21:34] [SUCCESS] ✅ 创建空配置成功: USA San Jose 08 -> ID: NRTtJpRu
[2025-08-04 12:21:36] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 01 -> ID: eca9n8WA
[2025-08-04 12:21:38] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 02 -> ID: MpaXYOvv
[2025-08-04 12:21:41] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 03 -> ID: JpLUwyDc
[2025-08-04 12:21:43] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 04 -> ID: v90TQ2nS
[2025-08-04 12:21:45] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 05 -> ID: l7Kmtv3F
[2025-08-04 12:21:48] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 06 -> ID: RqM3qnPX
[2025-08-04 12:21:50] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 07 -> ID: oRVRkFiR
[2025-08-04 12:21:53] [SUCCESS] ✅ 创建空配置成功: Russia St. Petersburg -> ID: gzVPhsGM
[2025-08-04 12:21:55] [SUCCESS] ✅ 创建空配置成功: Russia Moscow 01 -> ID: pvwUt0Mb
[2025-08-04 12:21:58] [SUCCESS] ✅ 创建空配置成功: Austria 01 -> ID: AuBNTTMs
[2025-08-04 12:22:00] [SUCCESS] ✅ 创建空配置成功: Australia Sydney 01 [Premium] -> ID: ztdmon86
[2025-08-04 12:22:03] [SUCCESS] ✅ 创建空配置成功: Australia Sydney 02 -> ID: cYCE3AVa
[2025-08-04 12:22:05] [SUCCESS] ✅ 创建空配置成功: Japan 01 -> ID: UDRafMJD
[2025-08-04 12:22:08] [SUCCESS] ✅ 创建空配置成功: Bulgaria 01 -> ID: 746yuAFv
[2025-08-04 12:22:11] [SUCCESS] ✅ 创建空配置成功: Japan 02 -> ID: YZgxMWd2
[2025-08-04 12:22:14] [SUCCESS] ✅ 创建空配置成功: Ireland 01 -> ID: 0xrihtnB
[2025-08-04 12:22:17] [SUCCESS] ✅ 创建空配置成功: Japan 03 -> ID: 7mZli6Fx
[2025-08-04 12:22:19] [INFO] 🔍 找到主配置ID: cfg013fd6
[2025-08-04 12:22:19] [INFO] 🔑 更新token: bafdd65661920942b73d8c50f0fa85a3 -> bafdd65661920942b73d8c50f0fa85a3
[2025-08-04 12:22:19] [INFO] 🔑 更新sessionid: 3c908fed0623046b040cbc93b1b1009c -> 3c908fed0623046b040cbc93b1b1009c
[2025-08-04 12:22:19] [INFO] 💾 批量提交请求包已保存到: 批量提交请求包_20250804_122219.txt
[2025-08-04 12:22:21] [INFO] 💾 批量提交响应包已保存到: 批量提交响应包_20250804_122221.txt
[2025-08-04 12:22:21] [SUCCESS] ✅ 批量提交成功！已设置 135 个配置
[2025-08-04 12:22:21] [INFO] ✅ 会话已失效，无需退出
[2025-08-04 12:22:21] [INFO] 开始登录OpenWrt后台

[2025-08-04 12:22:21] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
POST数据:
  luci_username: root
  luci_password: ********
------------------------------
[2025-08-04 12:22:22] === HTTP响应 登录请求 ===
状态码: 302
状态文本: Found
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  Set-Cookie: sysauth_http=347f5a84813d90d2a2a1b42bb23443ae; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  Location: /cgi-bin/luci/

🍪 重要Cookie信息:
  完整Cookie: sysauth_http=347f5a84813d90d2a2a1b42bb23443ae; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  ✅ 提取到sysauth_http: 347f5a84813d90d2a2a1b42bb23443ae

响应内容:

==================================================

[2025-08-04 12:22:22] [SUCCESS] ✅ 登录成功！Session ID: 347f5a84813d90d2a2a1b42bb23443ae
[2025-08-04 12:22:22] [INFO] 🔍 找到主配置ID: cfg013fd6
[2025-08-04 12:22:22] [INFO] 🔑 更新token: bafdd65661920942b73d8c50f0fa85a3 -> 2d49af76915de8b8258c8ec9766c3020
[2025-08-04 12:22:22] [INFO] 🔑 更新sessionid: 347f5a84813d90d2a2a1b42bb23443ae -> 347f5a84813d90d2a2a1b42bb23443ae
[2025-08-04 12:22:22] [INFO] 💾 批量提交请求包已保存到: 批量提交请求包_20250804_122222.txt
[2025-08-04 12:22:25] [INFO] 💾 批量提交响应包已保存到: 批量提交响应包_20250804_122225.txt
[2025-08-04 12:22:25] [SUCCESS] ✅ 批量提交成功！已设置 135 个配置
