OpenWrt Passwall 工具运行日志
==================================================

[2025-08-04 13:06:48] [INFO] 开始登录OpenWrt后台

[2025-08-04 13:06:48] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
POST数据:
  luci_username: root
  luci_password: ********
------------------------------
[2025-08-04 13:06:48] === HTTP响应 登录请求 ===
状态码: 302
状态文本: Found
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  Set-Cookie: sysauth_http=d49b1bb8a517e3abe48c9c17e4b319b8; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  Location: /cgi-bin/luci/

🍪 重要Cookie信息:
  完整Cookie: sysauth_http=d49b1bb8a517e3abe48c9c17e4b319b8; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  ✅ 提取到sysauth_http: d49b1bb8a517e3abe48c9c17e4b319b8

响应内容:

==================================================

[2025-08-04 13:06:48] [SUCCESS] ✅ 登录成功！Session ID: d49b1bb8a517e3abe48c9c17e4b319b8

[2025-08-04 13:06:48] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/node_list
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-04 13:06:51] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Node List
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touch-ico...
==================================================

[2025-08-04 13:06:51] [SUCCESS] ✅ 从节点列表页面成功获取到 135 个节点

[2025-08-04 13:06:51] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/settings
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-04 13:06:52] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Basic Settings
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touc...
==================================================

[2025-08-04 13:06:52] [SUCCESS] 🔑 成功提取token: c84fe15d0cf41b2ba19541d8c3b69f9a
[2025-08-04 13:06:52] [SUCCESS] 📋 已存在的socks配置: ['OODJHkc8', 'Pb2GVBQg', '7MvWVHUN', 'YbAtXnYi', 'KO7cDfqu', 'IsA7ofzt', 'KfdtRFrz', 'IlQM9b7b', '6UXDURmQ', '4TXHP58i', 'L82rskVh', 'C9hCHc6b', 'wuJnFrY1', 'XrqihbDt', 'wm0o3c2y', 'QeUdeeVw', 'faA6JVOW', 'SEFxo8Ng', '14IWgUBu', 'E4AWP8xz', 'XlAHeCKu', 'NBc0aNYC', 'nyMm7heg', 'EWCQu7In', '69CSD3GY', 'Zw42BAkk', 'Uh9J9OVm', '7q64QQbn', 'b4aionGy', 'zV5fiHD8', 'j2RFDukX', 'DwRleJ5k', 'fsyh6FZf', '5YZrNzBy', 'uDKcGklq', 'cANjhBKE', 'yIQ5y8qz', 'G2M2LL0e', 'G5KOvq1a', 'gArnF5hj', 'nkWGqA2U', 'IAJwErCg', 'SIhMGfAS', 'dQsuMbJV', 'jaDc1R7E', 'q0wFLZny', 'ZIS9rhQK', 'JbVDamMu', 'iupWS6Cl', 'gtvkIyFr', 'xDQeARfw', 'cb0hc7iZ', 'oEJQYSpi', 'trNGlzqr', 'D1TPyCGR', 'I5hhq22f', '7LKb4nd1', 'BWHtMyFz', 'IJdycLb8', 'qKL17DH8', '9knWgU54', 'EB1t8biE', 'pxa1TEFr', 'Djt0pfIx', 'VRno7smz', 'idTUpYS4', 'CTkxRJjo', 'PXbuLjfy', 'W6dr0UUh', 'KVekPYtA', 'VfZsITX8', '9zje2LSy', 'difRKTCg', 'h9Y6XYo4', '2lx27xxE', 'GUrJ9r3k', '5hHBV6xQ', 'Cx2roqFY', '3oWkxRiM', 'kLQvZwH1', 'Xllt215h', 'qUFUbpCE', '9z65biNQ', 'PMjGIgte', 'GReTymqu', '081ASAmC', '1OXx0kFx', 'NvjFPsR0', 'YycOoKK4', 'rqBeBhci', '3Vgs0AHM', 'QLgfRtdY', 'eOe86D8g', 'chDsa0T9', '8RTpxAuP', '8TYTs4wI', 'R4qkCp54', 'QwxmTXd9', 'm3KHk2AW', 'eqZdpEYZ', 'C7qRsoem', 'zd42ka8f', '1cgsY2Zw', 'wjqCFqNd', 'DvkllZsx', 'Ma2IARPK', 'Im2LO3Y2', 'YhxWy1To', 'd32ZVAUo', 'wIes3JHS', 'ydyHChif', 'TWoIqzzk', 'Ue2lTIoy', 'itfYGYyi', 'fNKvdDnk', 'Q2LxQPnU', 'RvwhgJ4J', 'miehAuGg', 'POABOZwO', 'xGxkl7sj', 's0DLhhfF', 'SgDN2PhU', 'rJPP16m9', 'rYyj4QH9', 'S5hKgE5r', 'xd1paqkT', 'GynVn2ER', 'lA6KDmZD', 'ueles8w3', 'DQvJPj9C', '7XOcHgmA', '7TB5WDgT', 'nATqrDoR', 'b79e2ha0', 'HyC1qOPj', '3J8o2yV3']

[2025-08-04 13:06:52] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/admin/services/passwall2/settings
请求头:
  Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
  Accept-Encoding: gzip, deflate
  Accept-Language: zh-CN,zh;q=0.9
  Cache-Control: max-age=0
  Origin: http://*************
  Referer: http://*************/cgi-bin/luci/admin/services/passwall2/settings
  Upgrade-Insecure-Requests: 1
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryACE5azrrYYWFcSp0
POST数据: multipart_data
------------------------------
[2025-08-04 13:06:52] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="zh-cn">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - åºæ¬è®¾ç½®
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-tou...
==================================================

[2025-08-04 13:06:52] [SUCCESS] ✅ 删除socks配置请求成功
[2025-08-04 13:06:52] [INFO] 💾 已更新nodes_data.json，共 1 个节点
[2025-08-04 13:06:53] [SUCCESS] ✅ 创建空配置成功: Hong Kong 01 -> ID: s1deNohp
