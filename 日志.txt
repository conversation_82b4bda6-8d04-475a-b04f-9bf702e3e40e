OpenWrt Passwall 工具运行日志
==================================================

[2025-08-04 13:36:52] [INFO] 开始登录OpenWrt后台

[2025-08-04 13:36:52] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
POST数据:
  luci_username: root
  luci_password: ********
------------------------------
[2025-08-04 13:36:52] === HTTP响应 登录请求 ===
状态码: 302
状态文本: Found
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  Set-Cookie: sysauth_http=a149beec9fc531fc3ab4626da8125067; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  Location: /cgi-bin/luci/

🍪 重要Cookie信息:
  完整Cookie: sysauth_http=a149beec9fc531fc3ab4626da8125067; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  ✅ 提取到sysauth_http: a149beec9fc531fc3ab4626da8125067

响应内容:

==================================================

[2025-08-04 13:36:52] [SUCCESS] ✅ 登录成功！Session ID: a149beec9fc531fc3ab4626da8125067

[2025-08-04 13:36:52] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/node_list
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-04 13:36:54] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Node List
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touch-ico...
==================================================

[2025-08-04 13:36:54] [SUCCESS] ✅ 从节点列表页面成功获取到 135 个节点

[2025-08-04 13:36:54] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/settings
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-04 13:36:54] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Basic Settings
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touc...
==================================================

[2025-08-04 13:36:54] [SUCCESS] 🔑 成功提取token: 38f681a2b61b1f003e113cfeda5a7f65
[2025-08-04 13:36:54] [SUCCESS] 📋 已存在的socks配置: ['3hhWmggO']

[2025-08-04 13:36:54] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/admin/services/passwall2/settings
请求头:
  Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
  Accept-Encoding: gzip, deflate
  Accept-Language: zh-CN,zh;q=0.9
  Cache-Control: max-age=0
  Origin: http://*************
  Referer: http://*************/cgi-bin/luci/admin/services/passwall2/settings
  Upgrade-Insecure-Requests: 1
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryACE5azrrYYWFcSp0
POST数据: multipart_data
------------------------------
[2025-08-04 13:36:54] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="zh-cn">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - åºæ¬è®¾ç½®
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-tou...
==================================================

[2025-08-04 13:36:54] [SUCCESS] ✅ 删除socks配置请求成功
[2025-08-04 13:36:54] [INFO] 💾 已更新nodes_data.json，共 135 个节点
[2025-08-04 13:36:55] [SUCCESS] ✅ 创建空配置成功: Hong Kong 01 -> ID: cR5e5hQl
[2025-08-04 13:36:55] [SUCCESS] ✅ 创建空配置成功: Hong Kong 02 -> ID: Hig3Gul8
[2025-08-04 13:36:55] [SUCCESS] ✅ 创建空配置成功: Hong Kong 03 -> ID: Kx3wjzoT
[2025-08-04 13:36:56] [SUCCESS] ✅ 创建空配置成功: Hong Kong 04 -> ID: 0k9OKsYx
[2025-08-04 13:36:56] [SUCCESS] ✅ 创建空配置成功: Hong Kong 05 -> ID: O7GigvWZ
[2025-08-04 13:36:56] [SUCCESS] ✅ 创建空配置成功: Hong Kong 06 -> ID: MCA0Yu1W
[2025-08-04 13:36:57] [SUCCESS] ✅ 创建空配置成功: Hong Kong 07 -> ID: HXZxMhP4
[2025-08-04 13:36:57] [SUCCESS] ✅ 创建空配置成功: Hong Kong 08 -> ID: 0HjjletM
[2025-08-04 13:36:58] [SUCCESS] ✅ 创建空配置成功: Hong Kong 09 -> ID: pv5jQWHZ
[2025-08-04 13:36:58] [SUCCESS] ✅ 创建空配置成功: Hong Kong 10 -> ID: zQkYm6Og
[2025-08-04 13:36:59] [SUCCESS] ✅ 创建空配置成功: Hong Kong 11 -> ID: zfuJRAKd
[2025-08-04 13:37:00] [SUCCESS] ✅ 创建空配置成功: Hong Kong 12 -> ID: xyI5Qg28
[2025-08-04 13:37:00] [SUCCESS] ✅ 创建空配置成功: Hong Kong 13 -> ID: 6gxQOTci
[2025-08-04 13:37:01] [SUCCESS] ✅ 创建空配置成功: Hong Kong 14 -> ID: Ee9Q2lsw
[2025-08-04 13:37:01] [SUCCESS] ✅ 创建空配置成功: Hong Kong 15 -> ID: 8E3tOtXO
[2025-08-04 13:37:02] [SUCCESS] ✅ 创建空配置成功: Hong Kong 16 -> ID: ICu4d8Wp
[2025-08-04 13:37:03] [SUCCESS] ✅ 创建空配置成功: Hong Kong 17 -> ID: 5CgMpKxq
[2025-08-04 13:37:04] [SUCCESS] ✅ 创建空配置成功: Hong Kong 18 -> ID: 6zAecDa3
[2025-08-04 13:37:05] [SUCCESS] ✅ 创建空配置成功: Hong Kong 19 -> ID: twblhRBJ
[2025-08-04 13:37:05] [SUCCESS] ✅ 创建空配置成功: Hong Kong 20 [Premium] -> ID: m5qOBJln
[2025-08-04 13:37:06] [SUCCESS] ✅ 创建空配置成功: Hong Kong 21 [Premium] -> ID: nXwGgZmh
[2025-08-04 13:37:07] [SUCCESS] ✅ 创建空配置成功: Hong Kong 22 [Premium] -> ID: ggFpijwy
[2025-08-04 13:37:08] [SUCCESS] ✅ 创建空配置成功: Hong Kong 23 -> ID: LpZo9jn0
[2025-08-04 13:37:09] [SUCCESS] ✅ 创建空配置成功: USA Seattle 01 -> ID: DeZNVzuq
[2025-08-04 13:37:10] [SUCCESS] ✅ 创建空配置成功: USA Seattle 02 -> ID: WYfk5KmH
[2025-08-04 13:37:11] [SUCCESS] ✅ 创建空配置成功: USA Seattle 03 -> ID: yNncNCp5
[2025-08-04 13:37:13] [SUCCESS] ✅ 创建空配置成功: USA Seattle 04 -> ID: 7dNbMq2H
[2025-08-04 13:37:14] [SUCCESS] ✅ 创建空配置成功: USA Seattle 05 -> ID: EICJz4RA
[2025-08-04 13:37:15] [SUCCESS] ✅ 创建空配置成功: USA Seattle 06 -> ID: 9T1Kco4C
[2025-08-04 13:37:16] [SUCCESS] ✅ 创建空配置成功: USA Seattle 07 -> ID: 21tY8iH5
[2025-08-04 13:37:17] [SUCCESS] ✅ 创建空配置成功: USA Seattle 08 -> ID: KJm1wh2o
[2025-08-04 13:37:19] [SUCCESS] ✅ 创建空配置成功: USA Seattle 09 -> ID: py7OGByI
[2025-08-04 13:37:20] [SUCCESS] ✅ 创建空配置成功: USA San Jose 01 [Premium] -> ID: uLlTAPzy
[2025-08-04 13:37:21] [SUCCESS] ✅ 创建空配置成功: USA San Jose 02 [Premium] -> ID: p5WuLx2R
[2025-08-04 13:37:23] [SUCCESS] ✅ 创建空配置成功: USA San Jose 03 [Premium] -> ID: dnyWdx0O
[2025-08-04 13:37:24] [SUCCESS] ✅ 创建空配置成功: USA San Jose 04 [Premium] -> ID: p1TOKHtI
[2025-08-04 13:37:26] [SUCCESS] ✅ 创建空配置成功: USA San Jose 05 [Premium] -> ID: eIZXmAuj
[2025-08-04 13:37:27] [SUCCESS] ✅ 创建空配置成功: USA San Jose 06 [Premium] -> ID: bOW3cCOU
[2025-08-04 13:37:29] [SUCCESS] ✅ 创建空配置成功: USA San Jose 07 -> ID: tqQ1tOrH
[2025-08-04 13:37:30] [SUCCESS] ✅ 创建空配置成功: USA San Jose 08 -> ID: XRqvTxdX
[2025-08-04 13:37:32] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 01 -> ID: 6ECz7zSR
[2025-08-04 13:37:34] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 02 -> ID: BimEDiFX
[2025-08-04 13:37:36] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 03 -> ID: m51NpsGM
[2025-08-04 13:37:37] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 04 -> ID: xsxjM44R
[2025-08-04 13:37:39] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 05 -> ID: MLHLfzv5
[2025-08-04 13:37:41] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 06 -> ID: vFr3c0fB
[2025-08-04 13:37:43] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 07 -> ID: S1G0LmMN
[2025-08-04 13:37:45] [SUCCESS] ✅ 创建空配置成功: Russia St. Petersburg -> ID: m8vFmJG0
[2025-08-04 13:37:47] [SUCCESS] ✅ 创建空配置成功: Russia Moscow 01 -> ID: NLxux6n2
[2025-08-04 13:37:49] [SUCCESS] ✅ 创建空配置成功: Austria 01 -> ID: LfNNeos6
[2025-08-04 13:37:51] [SUCCESS] ✅ 创建空配置成功: Australia Sydney 01 [Premium] -> ID: Pkk3OzBY
[2025-08-04 13:37:53] [SUCCESS] ✅ 创建空配置成功: Australia Sydney 02 -> ID: ml7rcZUv
[2025-08-04 13:37:55] [SUCCESS] ✅ 创建空配置成功: Japan 01 -> ID: JmBzIPt4
[2025-08-04 13:37:57] [SUCCESS] ✅ 创建空配置成功: Japan 02 -> ID: xbohEX8d
[2025-08-04 13:37:59] [SUCCESS] ✅ 创建空配置成功: Japan 03 -> ID: aFOWQscq
[2025-08-04 13:38:02] [SUCCESS] ✅ 创建空配置成功: Japan 04 -> ID: 3jpw6g6B
[2025-08-04 13:38:04] [SUCCESS] ✅ 创建空配置成功: Japan 05 -> ID: FPeptSiQ
[2025-08-04 13:38:06] [SUCCESS] ✅ 创建空配置成功: Japan 06 -> ID: DyorE7sS
[2025-08-04 13:38:09] [SUCCESS] ✅ 创建空配置成功: Japan 07 -> ID: PmeIWdsk
[2025-08-04 13:38:11] [SUCCESS] ✅ 创建空配置成功: Japan 08 -> ID: KoJKnlfg
[2025-08-04 13:38:14] [SUCCESS] ✅ 创建空配置成功: Japan 09 -> ID: msVSdPf9
[2025-08-04 13:38:16] [SUCCESS] ✅ 创建空配置成功: Japan 10 -> ID: CXq02MyG
[2025-08-04 13:38:19] [SUCCESS] ✅ 创建空配置成功: Japan 11 -> ID: gcdn4Xtj
[2025-08-04 13:38:21] [SUCCESS] ✅ 创建空配置成功: Japan 12 -> ID: VjAnZ6LJ
[2025-08-04 13:38:24] [SUCCESS] ✅ 创建空配置成功: Japan 13 -> ID: JkDluXqo
[2025-08-04 13:38:27] [SUCCESS] ✅ 创建空配置成功: Japan 14 -> ID: wERBRmSL
[2025-08-04 13:38:29] [SUCCESS] ✅ 创建空配置成功: Japan 15 -> ID: LIdAbA1v
[2025-08-04 13:38:32] [SUCCESS] ✅ 创建空配置成功: Japan 16 -> ID: EiwPwz5h
[2025-08-04 13:38:35] [INFO] 🔍 找到主配置ID: cfg013fd6
[2025-08-04 13:38:35] [INFO] 🔑 更新token: 38f681a2b61b1f003e113cfeda5a7f65 -> 38f681a2b61b1f003e113cfeda5a7f65
[2025-08-04 13:38:35] [INFO] 🔑 更新sessionid: a149beec9fc531fc3ab4626da8125067 -> a149beec9fc531fc3ab4626da8125067
[2025-08-04 13:38:35] [INFO] 💾 批量提交请求包已保存到: 批量提交请求包_20250804_133835.txt
[2025-08-04 13:38:37] [INFO] 💾 批量提交响应包已保存到: 批量提交响应包_20250804_133837.txt
[2025-08-04 13:38:37] [SUCCESS] ✅ 批量提交成功！已设置 135 个配置
