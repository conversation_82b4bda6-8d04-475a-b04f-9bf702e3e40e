批量提交请求包：
==================================================
POST /cgi-bin/luci/admin/services/passwall2/settings HTTP/1.1
Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryBatchSubmit
Cookie: sysauth_http=17b16fc1cf6c3ef4c4caf6dcc38347fd
Referer: http://*************/cgi-bin/luci/admin/services/passwall2/settings
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="token"

d73e5f0a36b3012be180b058366687af
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="sessionid"

17b16fc1cf6c3ef4c4caf6dcc38347fd
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbi.submit"

1
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbi.cbe.passwall2.cfg013fd6.enabled"

1
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbid.passwall2.cfg013fd6.node"

nil
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbi.cbe.passwall2.cfg013fd6.localhost_proxy"

1
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbid.passwall2.cfg013fd6.localhost_proxy"

1
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbi.cbe.passwall2.cfg013fd6.client_proxy"

1
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbid.passwall2.cfg013fd6.client_proxy"

1
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbid.passwall2.cfg013fd6.node_socks_port"

1070
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbid.passwall2.cfg013fd6.remote_dns_protocol"

tcp
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbid.passwall2.cfg013fd6.remote_dns"

*******
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbid.passwall2.cfg013fd6.remote_dns_detour"

remote
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbi.cbe.passwall2.cfg013fd6.remote_fakedns"

1
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbid.passwall2.cfg013fd6.remote_dns_query_strategy"

UseIPv4
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbi.cbe.passwall2.cfg013fd6.write_ipset_direct"

1
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbid.passwall2.cfg013fd6.write_ipset_direct"

1
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbi.cbe.passwall2.cfg013fd6.log_node"

1
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbid.passwall2.cfg013fd6.log_node"

1
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbid.passwall2.cfg013fd6.loglevel"

warning
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbi.cbe.passwall2.cfg013fd6.socks_enabled"

1
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbid.passwall2.cfg013fd6.socks_enabled"

1
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbi.cbe.passwall2.s1deNohp.enabled"

1
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbid.passwall2.s1deNohp.enabled"

1
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbid.passwall2.s1deNohp.node"

NHbySajH
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbid.passwall2.s1deNohp.port"

10001
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbid.passwall2.s1deNohp.http_port"

0
------WebKitFormBoundaryBatchSubmit
Content-Disposition: form-data; name="cbi.apply"

1
------WebKitFormBoundaryBatchSubmit--

==================================================
配置信息摘要：
主配置ID: cfg013fd6
Socks配置数量: 1

Socks配置详情：
  配置 1: s1deNohp -> 节点 Hong Kong 01 (NHbySajH) -> 端口 10001

总字段数: 28
字段构成: 主配置(16个) + Socks配置(1*5=5个) + 其他(3个) = 28个
