#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI配置文件 - 界面默认设置管理
=================================

这个文件用来管理软件界面的所有默认设置，包括：
- 窗口大小和是否可调整
- 各种选择框的默认选中状态
- 单选框的默认选择
- 输入框的默认数值
- 文本框的高度设置

修改方法：直接修改对应方法的返回值，保存后重启程序即可生效
"""

class UIConfig:
    """UI配置类 - 集中管理界面默认设置"""

    # ==================== 窗口设置 ====================
    @staticmethod
    def get_window_geometry():
        """
        设置软件窗口的大小和位置（居中显示）

        格式：宽度x高度+X偏移+Y偏移（单位：像素）
        常用尺寸：
        - "500x700"  # 紧凑模式，适合小屏幕
        - "500x900"  # 标准模式（当前设置）
        - "600x1000" # 大窗口模式，适合大屏幕
        """
        import tkinter as tk

        # 创建临时窗口来获取屏幕尺寸
        temp_root = tk.Tk()
        temp_root.withdraw()  # 隐藏临时窗口

        # 设置窗口大小
        window_width = 500
        window_height = 900

        # 获取屏幕尺寸
        screen_width = temp_root.winfo_screenwidth()
        screen_height = temp_root.winfo_screenheight()

        # 计算窗口居中位置
        center_x = int(screen_width/2 - window_width/2)
        center_y = int(screen_height/2 - window_height/2)

        # 销毁临时窗口
        temp_root.destroy()

        # 返回居中显示的窗口配置
        return f"{window_width}x{window_height}+{center_x}+{center_y}"

    @staticmethod
    def get_window_resizable():
        """
        设置窗口是否可以用鼠标拖拽调整大小

        返回格式：(宽度是否可调整, 高度是否可调整)
        - (True, True)   # 宽度和高度都可以调整（推荐）
        - (False, False) # 固定窗口大小，不可调整
        - (True, False)  # 只能调整宽度
        """
        return (True, True)  # 当前设置：宽度和高度都可以调整
    
    # ==================== 选择框默认状态 ====================
    @staticmethod
    def get_retry_login_default():
        """
        【二次登录重新提交】选择框的默认状态

        作用：当批量提交失败时，是否自动退出登录重新登录再试一次
        - True  = 默认选中（推荐）- 提高成功率
        - False = 默认不选中 - 只尝试一次提交
        """
        return False  # 当前设置：默认选中

    @staticmethod
    def get_save_create_files_default():
        """
        【保存配置创建请求包和响应包】选择框的默认状态

        作用：是否保存创建配置时的HTTP请求和响应数据到本地文件
        - True  = 默认选中 - 保存调试文件（开发调试时用）
        - False = 默认不选中（推荐）- 不保存，节省磁盘空间
        """
        return False  # 当前设置：默认不选中

    @staticmethod
    def get_save_delete_files_default():
        """
        【保存配置删除请求包】选择框的默认状态

        作用：是否保存删除配置时的HTTP请求数据到本地文件
        - True  = 默认选中 - 保存调试文件（开发调试时用）
        - False = 默认不选中（推荐）- 不保存，节省磁盘空间
        """
        return False  # 当前设置：默认不选中

    @staticmethod
    def get_save_token_files_default():
        """
        【保存token页面响应】选择框的默认状态

        作用：是否保存获取token时的页面响应数据到本地文件
        - True  = 默认选中 - 保存调试文件（开发调试时用）
        - False = 默认不选中（推荐）- 不保存，节省磁盘空间
        """
        return False  # 当前设置：默认不选中

    @staticmethod
    def get_save_batch_files_default():
        """
        【保存批量配置提交请求包和响应包】选择框的默认状态

        作用：是否保存批量提交配置时的HTTP请求和响应数据到本地文件
        - True  = 默认选中 - 保存调试文件（开发调试时用）
        - False = 默认不选中（推荐）- 不保存，节省磁盘空间
        """
        return True  # 当前设置：默认不选中
    
    # ==================== 单选框默认状态 ====================
    @staticmethod
    def get_processing_mode_default():
        """
        【处理模式】单选框的默认选择

        作用：选择处理节点的方式
        - "limit" = 限制模式（推荐）- 只处理前N个节点，适合测试
        - "batch" = 分批模式 - 将所有节点分批处理，适合大量节点
        """
        return "limit"  # 当前设置：限制模式

    # ==================== 输入框默认值 ====================
    @staticmethod
    def get_node_limit_default():
        """
        【节点数量限制】输入框的默认值（仅在限制模式下有效）

        作用：限制处理多少个节点
        - "0"  = 处理所有节点（当前设置）
        - "6"  = 只处理前6个节点（适合快速测试）
        - "20" = 只处理前20个节点
        - 任何正整数都可以
        """
        return "0"  # 当前设置：处理所有节点

    @staticmethod
    def get_batch_size_default():
        """
        【分批大小】输入框的默认值（仅在分批模式下有效）

        作用：每批处理多少个节点
        - "40" = 每批40个节点（当前设置，推荐）
        - "20" = 每批20个节点（适合网络较慢的情况）
        - "60" = 每批60个节点（适合网络较快的情况）
        """
        return "40"  # 当前设置：每批40个节点
    
    # ==================== 文本框设置 ====================
    @staticmethod
    def get_socks_text_height():
        """
        【Socks配置列表】文本框的高度

        作用：设置显示socks配置列表的文本框有多少行
        - 6  = 较小的文本框（适合紧凑模式）
        - 8  = 标准大小（当前设置）
        - 10 = 较大的文本框（适合大窗口模式）
        """
        return 8  # 当前设置：8行

    @staticmethod
    def get_log_text_height():
        """
        【日志显示】文本框的高度

        作用：设置显示程序运行日志的文本框有多少行
        - 6  = 较小的文本框（适合紧凑模式）
        - 8  = 标准大小（当前设置）
        - 12 = 较大的文本框（适合大窗口模式）
        """
        return 8  # 当前设置：8行
    
    # ==================== 快速配置预设 ====================
    # 注意：以下是配置预设说明，不是实际的配置方法
    # 要应用这些预设，请修改上面对应方法的返回值

    """
    🔧 常用配置预设：

    1️⃣ 开发调试模式：
       - get_save_create_files_default() 改为 True
       - get_save_delete_files_default() 改为 True
       - get_save_token_files_default() 改为 True
       - get_save_batch_files_default() 改为 True
       - get_node_limit_default() 改为 "6"

    2️⃣ 快速测试模式：
       - get_node_limit_default() 改为 "3"
       - get_processing_mode_default() 改为 "limit"
       - get_retry_login_default() 改为 False
       - 所有调试文件设为 False

    3️⃣ 批量生产模式：
       - get_processing_mode_default() 改为 "batch"
       - get_batch_size_default() 改为 "20"
       - get_retry_login_default() 改为 True
       - 所有调试文件设为 False

    4️⃣ 紧凑窗口模式：
       - get_window_geometry() 改为 "500x700"
       - get_socks_text_height() 改为 6
       - get_log_text_height() 改为 6

    5️⃣ 大窗口模式：
       - get_window_geometry() 改为 "600x1000"
       - get_socks_text_height() 改为 10
       - get_log_text_height() 改为 12
    """

# ==================== 使用说明 ====================
"""
使用方法：

1. 修改窗口大小：
   在 get_window_geometry() 方法中修改返回值
   例如：return "600x1000"  # 宽600像素，高1000像素

2. 修改选择框默认状态：
   在对应的方法中修改返回值
   例如：get_retry_login_default() 返回 True 表示默认选中

3. 修改单选框默认选择：
   在 get_processing_mode_default() 中修改返回值
   "limit" = 限制模式（默认）
   "batch" = 分批模式

4. 修改输入框默认值：
   在对应的方法中修改返回值
   例如：get_node_limit_default() 返回 "6" 表示默认限制6个节点

常用配置示例：

# 开发调试模式（启用所有调试文件）
def get_save_create_files_default(): return True
def get_save_delete_files_default(): return True
def get_save_token_files_default(): return True
def get_save_batch_files_default(): return True

# 生产模式（禁用所有调试文件）
def get_save_create_files_default(): return False
def get_save_delete_files_default(): return False
def get_save_token_files_default(): return False
def get_save_batch_files_default(): return False

# 测试模式（限制6个节点）
def get_node_limit_default(): return "6"
def get_processing_mode_default(): return "limit"

# 批量模式（每批20个节点）
def get_batch_size_default(): return "20"
def get_processing_mode_default(): return "batch"
"""
